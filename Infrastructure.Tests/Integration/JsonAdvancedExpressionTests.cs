using Domain.Entities;
using BusinessAutomation.Core.Abstractions.ExpressionEngine.Models;
using BusinessAutomation.Core.Abstractions.ExpressionEngine.Services;
using BusinessAutomation.Core.Infrastructure.ExpressionEngine.Core;
using BusinessAutomation.Core.Infrastructure.ExpressionEngine.Services;
using BusinessAutomation.Core.Infrastructure.Database.Conversions;
using BusinessAutomation.Core.Infrastructure.Database.Persistence;
using BusinessAutomation.Core.Abstractions.Database;
using Infrastructure.Persistence;
using Infrastructure.Services;
using Infrastructure.Tests.Mocks;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Caching.Memory;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Logging;
using System.Text.Json;
using Xunit;

namespace Infrastructure.Tests.Integration;

/// <summary>
/// Pokročilé testy pro Expression engine s JSON vstupem
/// Testuje Lookup, Aggregation a složité cross-entity scénáře z JSON
/// </summary>
public class JsonAdvancedExpressionTests : IDisposable
{
    private readonly ApplicationDbContext _context;
    private readonly BusinessAutomationDbContext _businessContext;
    private readonly CalculationEngine _calculationEngine;
    private readonly IExpressionRepository _expressionRepository;
    private readonly ServiceProvider _serviceProvider;
    private readonly JsonSerializerOptions _jsonOptions;

    public JsonAdvancedExpressionTests()
    {
        var services = new ServiceCollection();
        
        // Registrace mock služeb
        services.AddScoped<Application.Abstraction.ICurrentUserService, Infrastructure.Tests.Mocks.MockCurrentUserService>();
        services.AddScoped<Application.Services.Events.DomainEventPublisher, Infrastructure.Tests.Mocks.MockDomainEventPublisher>();

        // Registrace interceptorů
        services.AddScoped<Infrastructure.Persistence.Interceptors.TrackableEntityInterceptor>();
        services.AddScoped<Infrastructure.Persistence.Interceptors.AuditableEntityInterceptor>();

        // Registrace cache služeb
        services.AddSingleton<IMemoryCache, MemoryCache>();
        services.AddScoped<Application.Abstraction.ICacheService, MemoryCacheService>();

        // Registrace logging
        services.AddLogging(builder => builder.AddConsole());

        // Registrace DbContext se skutečnou SQLite databází pro testy
        var baseDirectory = Directory.GetCurrentDirectory();
        var dataDirectory = Path.Combine(baseDirectory, "Data");
        var connectionString = $"Data Source={Path.Combine(dataDirectory, "datacapture.db")}";

        services.AddDbContext<ApplicationDbContext>((serviceProvider, options) =>
        {
            var trackableInterceptor = serviceProvider.GetRequiredService<Infrastructure.Persistence.Interceptors.TrackableEntityInterceptor>();
            var auditableInterceptor = serviceProvider.GetRequiredService<Infrastructure.Persistence.Interceptors.AuditableEntityInterceptor>();

            options.UseSqlite(connectionString)
                   .AddInterceptors(trackableInterceptor, auditableInterceptor);
        });

        // Registrace BusinessAutomation DbContext s in-memory databází pro testy
        services.AddDbContext<BusinessAutomationDbContext>(options =>
        {
            options.UseInMemoryDatabase(databaseName: Guid.NewGuid().ToString());
        });

        // Registrace IApplicationDbContext a IBusinessAutomationDbContext
        services.AddScoped<Application.Abstraction.IApplicationDbContext>(provider =>
            provider.GetRequiredService<ApplicationDbContext>());
        services.AddScoped<IBusinessAutomationDbContext>(provider =>
            provider.GetRequiredService<BusinessAutomationDbContext>());

        // Registrace BusinessAutomation ExpressionEngine služeb
        services.AddSingleton<IExpressionCacheService, ExpressionCacheService>();
        services.AddScoped<IExpressionRepository, ExpressionRepository>();
        services.AddScoped<IExpressionDataProvider, ExpressionDataProvider>();

        // Registrace entity type map
        services.AddSingleton<IReadOnlyDictionary<string, Type>>(provider =>
        {
            var entityTypes = new Dictionary<string, Type>
            {
                { "SampleEntity", typeof(Domain.Entities.SampleEntity) },
                { "Order", typeof(Domain.Entities.Order) },
                { "OrderItem", typeof(Domain.Entities.OrderItem) },
                { "Expression", typeof(BusinessAutomation.Core.Abstractions.ExpressionEngine.Models.Expression) }
            };
            return entityTypes;
        });
        
        // Registrace specializovaných builderů
        services.AddScoped<BusinessAutomation.Core.Abstractions.ExpressionEngine.Builders.IConstantExpressionBuilder, BusinessAutomation.Core.Infrastructure.ExpressionEngine.Builders.ConstantExpressionBuilder>();
        services.AddScoped<BusinessAutomation.Core.Abstractions.ExpressionEngine.Builders.ISourceExpressionBuilder>(provider =>
        {
            var propertyMetadata = provider.GetService<IReadOnlyDictionary<string, System.Reflection.PropertyInfo>>();
            return new BusinessAutomation.Core.Infrastructure.ExpressionEngine.Builders.SourceExpressionBuilder(propertyMetadata);
        });
        services.AddScoped<BusinessAutomation.Core.Abstractions.ExpressionEngine.Builders.IOperationExpressionBuilder, BusinessAutomation.Core.Infrastructure.ExpressionEngine.Builders.OperationExpressionBuilder>();
        services.AddScoped<BusinessAutomation.Core.Abstractions.ExpressionEngine.Builders.ILookupExpressionBuilder>(provider =>
        {
            var dataProvider = provider.GetRequiredService<IExpressionDataProvider>();
            var entityTypeMap = provider.GetRequiredService<IReadOnlyDictionary<string, Type>>();
            return new BusinessAutomation.Core.Infrastructure.ExpressionEngine.Builders.LookupExpressionBuilder(dataProvider, entityTypeMap);
        });
        services.AddScoped<BusinessAutomation.Core.Abstractions.ExpressionEngine.Builders.IAggregationExpressionBuilder>(provider =>
        {
            var dataProvider = provider.GetRequiredService<IExpressionDataProvider>();
            var entityTypeMap = provider.GetRequiredService<IReadOnlyDictionary<string, Type>>();
            return new BusinessAutomation.Core.Infrastructure.ExpressionEngine.Builders.AggregationExpressionBuilder(dataProvider, entityTypeMap);
        });
        
        // Registrace kompozitního ExpressionBuilder
        services.AddScoped<BusinessAutomation.Core.Abstractions.ExpressionEngine.Builders.IExpressionBuilder, ExpressionBuilder>();

        // Registrace EntityMetadataService
        services.AddScoped<BusinessAutomation.Core.Infrastructure.Common.Services.EntityMetadataService>();

        // Registrace CalculationEngine
        services.AddScoped<CalculationEngine>();

        _serviceProvider = services.BuildServiceProvider();
        _context = _serviceProvider.GetRequiredService<ApplicationDbContext>();
        _businessContext = _serviceProvider.GetRequiredService<BusinessAutomationDbContext>();
        _calculationEngine = _serviceProvider.GetRequiredService<CalculationEngine>();
        _expressionRepository = _serviceProvider.GetRequiredService<IExpressionRepository>();
        _jsonOptions = DefaultJsonSerializerOptions.Options;
        
        // Zajištění existence BusinessAutomation databáze (ApplicationDbContext používá existující databázi)
        _businessContext.Database.EnsureCreated();
    }

    /// <summary>
    /// Pomocná metoda pro vytvoření Expression z JSON
    /// </summary>
    private async Task<Expression> CreateExpressionFromJson(string name, string description, string jsonExpressionTree)
    {
        var expressionTree = JsonSerializer.Deserialize<ExpressionNode>(jsonExpressionTree, _jsonOptions);
        
        var expression = new Expression
        {
            Id = Guid.NewGuid(),
            Name = name,
            Description = description,
            ExpressionTree = expressionTree!,
            IsActive = true,
            Version = 1,
            EffectiveFrom = DateTime.UtcNow,
            SchemaVersion = "1.0"
            // RowVersion se automaticky spravuje EF Core
        };

        await _expressionRepository.AddAsync(expression);
        return expression;
    }

    #region Lookup Node JSON Tests

    [Fact]
    public async Task JsonLookup_FindOrderByNumber_ShouldReturnValue()
    {
        // Arrange - vytvoříme testovací data v databázi s unikátním OrderNumber
        var uniqueOrderNumber = $"TEST-ORDER-{Guid.NewGuid().ToString()[..8]}";
        var testOrder = new Order
        {
            Id = Guid.NewGuid(),
            OrderNumber = uniqueOrderNumber,
            OrderDate = DateTime.Now,
            TotalAmount = 1500m,
            Status = Domain.Entities.OrderStatus.Processing,
            CustomerName = "Test Customer",
            CustomerEmail = "<EMAIL>"
        };

        _context.Set<Order>().Add(testOrder);
        await _context.SaveChangesAsync();

        var jsonExpression = $$"""
        {
            "NodeType": "Lookup",
            "TargetEntityName": "Order",
            "ReturnFieldPath": "TotalAmount",
            "Condition": {
                "NodeType": "Operation",
                "Operator": "Equal",
                "Operands": [
                    {
                        "NodeType": "SourceValue",
                        "SourcePath": "OrderNumber"
                    },
                    {
                        "NodeType": "Constant",
                        "DataType": "String",
                        "Value": "{{uniqueOrderNumber}}"
                    }
                ]
            }
        }
        """;

        var expression = await CreateExpressionFromJson(
            "JSON Lookup Order Total",
            "Najde objednávku podle čísla a vrátí její celkovou částku z JSON",
            jsonExpression);

        var testEntity = new SampleEntity
        {
            Id = 1,
            Name = "Test",
            DateOfBirth = DateTime.Now.AddYears(-25),
            IsActive = true
        };

        // Act
        var result = await _calculationEngine.ExecuteAsync(expression, testEntity);

        // Assert - očekáváme TotalAmount pro naši testovací objednávku
        Assert.Equal(1500m, Convert.ToDecimal((decimal)result));
    }

    [Fact]
    public async Task JsonLookup_FindOrderByCustomer_ShouldReturnValue()
    {
        // Arrange - vytvoříme testovací data v databázi s unikátním OrderNumber a CustomerName
        var uniqueOrderNumber = $"TEST-ORDER-{Guid.NewGuid().ToString()[..8]}";
        var testCustomerName = $"Test Customer Marie {Guid.NewGuid().ToString()[..8]}";
        var testOrder = new Order
        {
            Id = Guid.NewGuid(),
            OrderNumber = uniqueOrderNumber,
            OrderDate = DateTime.Now,
            TotalAmount = 49450m,
            Status = Domain.Entities.OrderStatus.Shipped,
            CustomerName = testCustomerName,
            CustomerEmail = "<EMAIL>"
        };

        _context.Set<Order>().Add(testOrder);
        await _context.SaveChangesAsync();

        var jsonExpression = $$"""
        {
            "NodeType": "Lookup",
            "TargetEntityName": "Order",
            "ReturnFieldPath": "Status",
            "Condition": {
                "NodeType": "Operation",
                "Operator": "Equal",
                "Operands": [
                    {
                        "NodeType": "SourceValue",
                        "SourcePath": "CustomerName"
                    },
                    {
                        "NodeType": "Constant",
                        "DataType": "String",
                        "Value": "{{testCustomerName}}"
                    }
                ]
            }
        }
        """;

        var expression = await CreateExpressionFromJson(
            "JSON Lookup Customer Order Status",
            "Najde objednávku podle jména zákazníka a vrátí její status z JSON",
            jsonExpression);

        var testEntity = new SampleEntity
        {
            Id = 2,
            Name = "Test",
            DateOfBirth = DateTime.Now.AddYears(-25),
            IsActive = true
        };

        // Act
        var result = await _calculationEngine.ExecuteAsync(expression, testEntity);

        // Assert - očekáváme Status pro naši testovací objednávku (Shipped = 3)
        Assert.Equal(Domain.Entities.OrderStatus.Shipped, (Domain.Entities.OrderStatus)result);
    }

    #endregion

    #region Aggregation Node JSON Tests

    [Fact]
    public async Task JsonAggregation_CountItems_ShouldReturnValue()
    {
        // Arrange - vytvoření objednávky s položkami s unikátním OrderNumber
        var uniqueOrderNumber = $"TEST-ORDER-{Guid.NewGuid().ToString()[..8]}";
        var order = new Order
        {
            Id = Guid.NewGuid(),
            OrderNumber = uniqueOrderNumber,
            OrderDate = DateTime.Now,
            TotalAmount = 1000m,
            Status = Domain.Entities.OrderStatus.Processing,
            CustomerName = "Test Customer",
            CustomerEmail = "<EMAIL>"
        };

        var orderItems = new List<OrderItem>
        {
            new OrderItem
            {
                Id = Guid.NewGuid(),
                OrderId = order.Id,
                ProductCode = "PROD1",
                ProductName = "Product 1",
                Quantity = 2,
                UnitPrice = 100m,
                LineTotalWithTax = 200m
            },
            new OrderItem
            {
                Id = Guid.NewGuid(),
                OrderId = order.Id,
                ProductCode = "PROD2",
                ProductName = "Product 2",
                Quantity = 1,
                UnitPrice = 300m,
                LineTotalWithTax = 300m
            }
        };

        _context.Set<Order>().Add(order);
        _context.Set<OrderItem>().AddRange(orderItems);
        await _context.SaveChangesAsync();

        var jsonExpression = """
        {
            "NodeType": "Aggregation",
            "AggregationType": "Count",
            "CollectionPath": "Items"
        }
        """;

        var expression = await CreateExpressionFromJson(
            "JSON Count Items",
            "Počet položek v objednávce z JSON",
            jsonExpression);

        // Act
        var result = await _calculationEngine.ExecuteAsync(expression, order);

        // Assert
        Assert.Equal(2, Convert.ToInt32(result));
    }

    #endregion

    public void Dispose()
    {
        _context?.Dispose();
        _businessContext?.Dispose();
        _serviceProvider?.Dispose();
    }
}
