using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.OpenApi.Models;
using Swashbuckle.AspNetCore.SwaggerGen;
using System.Collections.Concurrent;
using System.Text.RegularExpressions;

namespace API.Filters;

/// <summary>
/// Schema filter pro přenos EF Core validačních pravidel do OpenAPI dokumentace
/// </summary>
public class EfCoreSchemaFilter : ISchemaFilter
{
    private static readonly ConcurrentDictionary<string, IModel?> _modelCache = new();
    private static readonly object _lockObject = new();

    public EfCoreSchemaFilter()
    {
    }
    public void Apply(OpenApiSchema schema, SchemaFilterContext context)
    {
        // Jen objekty s vlastnostmi
        if (schema.Properties == null || schema.Properties.Count == 0)
            return;

        try
        {
            // Získej EF model bez připojení k databázi
            var model = GetCachedModel();
            if (model == null)
                return;

            // Najdi EF entitu pro daný typ nebo mapovanou entitu
            var entityType = FindEntityType(model, context.Type);
            if (entityType == null)
                return;

        // Zpracování entity-level anotací
        ProcessEntityLevelAnnotations(schema, entityType);

        foreach (var efProp in entityType.GetProperties())
        {
            // Jméno property v schématu (camelCase)
            var propertyName = ToCamelCase(efProp.Name);
            if (!schema.Properties.TryGetValue(propertyName, out var propSchema))
                continue;

            // Standardní EF Core vlastnosti
            ProcessStandardEfCoreProperties(schema, efProp, propSchema, propertyName);

            // Custom validační anotace
            ProcessCustomValidationAnnotations(efProp, propSchema);
        }
        }
        catch (Exception)
        {
            // Pokud se nepodaří načíst EF Core metadata, ignorujeme chybu
            // a necháme schéma bez EF Core rozšíření
            return;
        }
    }

    /// <summary>
    /// Získá EF model z cache nebo ho vytvoří bez připojení k databázi
    /// </summary>
    private static IModel? GetCachedModel()
    {
        const string cacheKey = "EfCoreModel";

        if (_modelCache.TryGetValue(cacheKey, out var cachedModel))
        {
            return cachedModel;
        }

        lock (_lockObject)
        {
            // Double-check locking pattern
            if (_modelCache.TryGetValue(cacheKey, out cachedModel))
            {
                return cachedModel;
            }

            try
            {
                // Vytvoř EF model bez připojení k databázi
                var optionsBuilder = new DbContextOptionsBuilder<Infrastructure.Persistence.ApplicationDbContext>();

                // Použij in-memory databázi pouze pro získání modelu
                optionsBuilder.UseInMemoryDatabase("MetadataOnly");

                // Vytvoř mock objekty pro závislosti
                var mockCurrentUserService = new MockCurrentUserService();
                var mockDomainEventPublisher = new MockDomainEventPublisher();

                using var context = new Infrastructure.Persistence.ApplicationDbContext(
                    optionsBuilder.Options,
                    mockCurrentUserService,
                    mockDomainEventPublisher);
                var model = context.Model;

                _modelCache.TryAdd(cacheKey, model);
                return model;
            }
            catch (Exception)
            {
                _modelCache.TryAdd(cacheKey, null);
                return null;
            }
        }
    }

    /// <summary>
    /// Zpracovává entity-level anotace
    /// </summary>
    private static void ProcessEntityLevelAnnotations(OpenApiSchema schema, Microsoft.EntityFrameworkCore.Metadata.IEntityType entityType)
    {
        // Přidání entity-level validačních pravidel do extensions
        var extensions = new Dictionary<string, object>();

        // Business rules
        if (entityType.FindAnnotation("BusinessRules")?.Value is string[] businessRules)
            extensions["x-business-rules"] = businessRules;

        // Cross-field validation
        if (entityType.FindAnnotation("CrossFieldValidation")?.Value is string[] crossFieldValidation)
            extensions["x-cross-field-validation"] = crossFieldValidation;

        // Conditional validation
        if (entityType.FindAnnotation("ConditionalValidation")?.Value is string[] conditionalValidation)
            extensions["x-conditional-validation"] = conditionalValidation;

        // Custom validators
        if (entityType.FindAnnotation("CustomValidators")?.Value is string[] customValidators)
            extensions["x-custom-validators"] = customValidators;

        // Validation groups
        if (entityType.FindAnnotation("ValidationGroups")?.Value is string[] validationGroups)
            extensions["x-validation-groups"] = validationGroups;

        // Validation order
        if (entityType.FindAnnotation("ValidationOrder")?.Value is string[] validationOrder)
            extensions["x-validation-order"] = validationOrder;

        // Entity validation type
        if (entityType.FindAnnotation("EntityValidation")?.Value is string entityValidation)
            extensions["x-entity-validation"] = entityValidation;

        // Přidání extensions do schématu
        foreach (var extension in extensions)
        {
            var openApiArray = new Microsoft.OpenApi.Any.OpenApiArray();

            if (extension.Value is string[] array)
            {
                foreach (var item in array)
                {
                    openApiArray.Add(new Microsoft.OpenApi.Any.OpenApiString(item));
                }
            }
            else
            {
                openApiArray.Add(new Microsoft.OpenApi.Any.OpenApiString(extension.Value.ToString() ?? ""));
            }

            schema.Extensions[extension.Key] = openApiArray;
        }
    }

    /// <summary>
    /// Zpracovává standardní EF Core vlastnosti
    /// </summary>
    private static void ProcessStandardEfCoreProperties(OpenApiSchema schema, Microsoft.EntityFrameworkCore.Metadata.IProperty efProp, OpenApiSchema propSchema, string propertyName)
    {
        // 1) Povinné
        if (!efProp.IsNullable && !schema.Required.Contains(propertyName))
            schema.Required.Add(propertyName);

        // 2) MaxLength pro stringy
        if (efProp.ClrType == typeof(string) && efProp.GetMaxLength().HasValue)
            propSchema.MaxLength = efProp.GetMaxLength()!.Value;

        // 3) Precise decimal → minimum/maximum
        if (efProp.ClrType == typeof(decimal)
            && efProp.GetPrecision().HasValue
            && efProp.GetScale().HasValue)
        {
            var p = efProp.GetPrecision()!.Value;
            var s = efProp.GetScale()!.Value;
            var intDigits = p - s;
            propSchema.Maximum = (decimal)Math.Pow(10, intDigits) - (decimal)Math.Pow(10, -s);
            propSchema.Minimum = 0;
        }

        // 4) Popisek z HasComment() - pouze pokud je dostupný
        try
        {
            var comment = efProp.GetComment();
            if (!string.IsNullOrWhiteSpace(comment))
                propSchema.Description = comment;
        }
        catch (InvalidOperationException)
        {
            // GetComment() není dostupný v read-optimized modelu, ignorujeme
        }
    }

    /// <summary>
    /// Zpracovává custom validační anotace
    /// </summary>
    private static void ProcessCustomValidationAnnotations(Microsoft.EntityFrameworkCore.Metadata.IProperty efProp, OpenApiSchema propSchema)
    {
        var annotations = efProp.GetAnnotations().ToDictionary(a => a.Name, a => a.Value);

        // MinLength
        if (annotations.TryGetValue("MinLength", out var minLength) && int.TryParse(minLength?.ToString(), out var minLengthValue))
            propSchema.MinLength = minLengthValue;

        // Pattern (regex)
        if (annotations.TryGetValue("Pattern", out var pattern) && pattern?.ToString() is string patternStr)
            propSchema.Pattern = patternStr;

        // Display name
        if (annotations.TryGetValue("Display", out var display) && display?.ToString() is string displayStr)
            propSchema.Title = displayStr;

        // Error message
        if (annotations.TryGetValue("ErrorMessage", out var errorMessage) && errorMessage?.ToString() is string errorStr)
            propSchema.Extensions["x-error-message"] = new Microsoft.OpenApi.Any.OpenApiString(errorStr);

        // Placeholder
        if (annotations.TryGetValue("Placeholder", out var placeholder) && placeholder?.ToString() is string placeholderStr)
            propSchema.Extensions["x-placeholder"] = new Microsoft.OpenApi.Any.OpenApiString(placeholderStr);

        // Required flag
        if (annotations.TryGetValue("Required", out var required) && bool.TryParse(required?.ToString(), out var requiredValue))
            propSchema.Extensions["x-required"] = new Microsoft.OpenApi.Any.OpenApiBoolean(requiredValue);

        // Input type
        if (annotations.TryGetValue("Type", out var type) && type?.ToString() is string typeStr)
            propSchema.Extensions["x-input-type"] = new Microsoft.OpenApi.Any.OpenApiString(typeStr);

        // Range validation
        if (annotations.TryGetValue("Range", out var range) && range?.ToString() is string rangeStr)
        {
            var parts = rangeStr.Split(',');
            if (parts.Length == 2 && decimal.TryParse(parts[0], out var min) && decimal.TryParse(parts[1], out var max))
            {
                propSchema.Minimum = min;
                propSchema.Maximum = max;
            }
        }

        // Min/Max values
        if (annotations.TryGetValue("Min", out var minValue) && decimal.TryParse(minValue?.ToString(), out var minVal))
            propSchema.Minimum = minVal;

        if (annotations.TryGetValue("Max", out var maxValue) && decimal.TryParse(maxValue?.ToString(), out var maxVal))
            propSchema.Maximum = maxVal;

        // Step for numeric inputs
        if (annotations.TryGetValue("Step", out var step) && decimal.TryParse(step?.ToString(), out var stepVal))
            propSchema.Extensions["x-step"] = new Microsoft.OpenApi.Any.OpenApiDouble((double)stepVal);

        // Data type
        if (annotations.TryGetValue("DataType", out var dataType) && dataType?.ToString() is string dataTypeStr)
            propSchema.Extensions["x-data-type"] = new Microsoft.OpenApi.Any.OpenApiString(dataTypeStr);

        // Display format
        if (annotations.TryGetValue("DisplayFormat", out var displayFormat) && displayFormat?.ToString() is string displayFormatStr)
            propSchema.Extensions["x-display-format"] = new Microsoft.OpenApi.Any.OpenApiString(displayFormatStr);

        // Multiline flag
        if (annotations.TryGetValue("Multiline", out var multiline) && bool.TryParse(multiline?.ToString(), out var multilineValue))
            propSchema.Extensions["x-multiline"] = new Microsoft.OpenApi.Any.OpenApiBoolean(multilineValue);

        // Rows for textarea
        if (annotations.TryGetValue("Rows", out var rows) && int.TryParse(rows?.ToString(), out var rowsValue))
            propSchema.Extensions["x-rows"] = new Microsoft.OpenApi.Any.OpenApiInteger(rowsValue);

        // Default value
        if (annotations.TryGetValue("DefaultValue", out var defaultValue))
        {
            propSchema.Extensions["x-default-value"] = defaultValue switch
            {
                bool boolVal => new Microsoft.OpenApi.Any.OpenApiBoolean(boolVal),
                int intVal => new Microsoft.OpenApi.Any.OpenApiInteger(intVal),
                double doubleVal => new Microsoft.OpenApi.Any.OpenApiDouble(doubleVal),
                decimal decimalVal => new Microsoft.OpenApi.Any.OpenApiDouble((double)decimalVal),
                _ => new Microsoft.OpenApi.Any.OpenApiString(defaultValue?.ToString() ?? "")
            };
        }

        // Confidential flag
        if (annotations.TryGetValue("Confidential", out var confidential) && bool.TryParse(confidential?.ToString(), out var confidentialValue))
            propSchema.Extensions["x-confidential"] = new Microsoft.OpenApi.Any.OpenApiBoolean(confidentialValue);

        // Access level
        if (annotations.TryGetValue("AccessLevel", out var accessLevel) && accessLevel?.ToString() is string accessLevelStr)
            propSchema.Extensions["x-access-level"] = new Microsoft.OpenApi.Any.OpenApiString(accessLevelStr);
    }

    /// <summary>
    /// Najde EF entitu pro daný typ nebo mapovanou entitu
    /// </summary>
    private static Microsoft.EntityFrameworkCore.Metadata.IEntityType? FindEntityType(IModel model, Type type)
    {
        // Nejprve zkus najít přímo
        var entityType = model.FindEntityType(type);
        if (entityType != null)
            return entityType;

        // Mapování DTO typů na entity
        var entityTypeName = MapDtoToEntityType(type);
        if (entityTypeName != null)
        {
            entityType = model.FindEntityType(entityTypeName);
            if (entityType != null)
                return entityType;
        }

        return null;
    }

    /// <summary>
    /// Mapuje DTO typ na odpovídající entitu
    /// </summary>
    private static Type? MapDtoToEntityType(Type dtoType)
    {
        // Mapování podle konvencí
        var typeName = dtoType.Name;

        // SampleDto -> SampleEntity
        if (typeName == "SampleDto" || typeName == "SampleAddEdit")
            return typeof(Domain.Entities.SampleEntity);

        // OrderDto -> Order
        if (typeName == "OrderDto" || typeName == "OrderAddEdit")
            return typeof(Domain.Entities.Order);

        // OrderItemDto -> OrderItem
        if (typeName == "OrderItemDto" || typeName == "OrderItemAddEdit")
            return typeof(Domain.Entities.OrderItem);

        // InvoiceDto -> Invoice
        if (typeName == "InvoiceDto" || typeName == "InvoiceAddEdit")
            return typeof(Domain.Entities.Invoice);

        // InvoiceItemDto -> InvoiceItem
        if (typeName == "InvoiceItemDto" || typeName == "InvoiceItemAddEdit")
            return typeof(Domain.Entities.InvoiceItem);

        return null;
    }

    /// <summary>
    /// Převede PascalCase na camelCase
    /// </summary>
    private static string ToCamelCase(string input)
    {
        if (string.IsNullOrEmpty(input) || char.IsLower(input[0]))
            return input;

        return char.ToLowerInvariant(input[0]) + input[1..];
    }
}

/// <summary>
/// Mock implementace ICurrentUserService pro získání EF modelu
/// </summary>
internal class MockCurrentUserService : Application.Abstraction.ICurrentUserService
{
    public string? UserId => null;
    public string? Email => null;
    public IEnumerable<string> Roles => Enumerable.Empty<string>();
    public Domain.Identity.UserProfile? Profile => null;
}

/// <summary>
/// Mock implementace DomainEventPublisher pro získání EF modelu
/// </summary>
internal class MockDomainEventPublisher : Application.Services.Events.DomainEventPublisher
{
    public MockDomainEventPublisher() : base(null!)
    {
    }
}