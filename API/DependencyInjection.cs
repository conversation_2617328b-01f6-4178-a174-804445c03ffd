using System.Text.Json.Serialization;
using API.ApiService;
using API.Endpoints;
using API.Filters;
using BusinessAutomation.Core.Infrastructure.Events.Endpoints;
using BusinessAutomation.Core.Infrastructure.ExpressionEngine.API;
using Infrastructure;


namespace API;

/// <summary>
/// Registrace služeb a konfigurace specifické pro API vrstvu
/// </summary>
public static class DependencyInjection
{
    /// <summary>
    /// Registruje služby specifické pro API vrstvu
    /// </summary>
    /// <param name="services">Kolekce služeb</param>
    /// <returns>Kolekce služeb pro fluent API</returns>
    public static IServiceCollection AddApiServices(this IServiceCollection services)
    {
        // Registrace API exploreru a OpenAPI
        services.AddEndpointsApiExplorer();
        services.AddSwaggerGen(c =>
        {
            c.SwaggerDoc("v1", new Microsoft.OpenApi.Models.OpenApiInfo
            {
                Title = "DataCapture API",
                Version = "v1"
            });

            // Všechny vlastnosti budou považovány za povinné, pokud nejsou explicitně označeny jako volitelné
            c.SchemaFilter<RequiredNotNullableSchemaFilter>();
            // EF Core validační pravidla do Swagger schématu
            c.SchemaFilter<EfCoreSchemaFilter>();
        });

        // Konfigurace kontrolerů a JSON serializace
        services.AddControllers()
            .AddJsonOptions(options =>
            {
                options.JsonSerializerOptions.Converters.Add(new JsonStringEnumConverter());
            });

        // Registrace API služeb
        services.AddScoped(typeof(ICrudApiService<>), typeof(CrudApiService<>));

        return services;
    }

    /// <summary>
    /// Konfiguruje API endpointy a middleware
    /// </summary>
    /// <param name="app">WebApplication instance</param>
    /// <returns>WebApplication pro fluent API</returns>
    public static WebApplication UseApiConfiguration(this WebApplication app)
    {
        // Swagger konfigurace
        app.UseSwagger();
        app.UseSwaggerUI(options =>
        {
            options.SwaggerEndpoint("/swagger/v1/swagger.json", "DataCapture API V1");
            // options.RoutePrefix = string.Empty; // Swagger UI na root / - způsobuje problémy
        });

        // Redirect root na Swagger UI
        app.MapGet("/", () => Results.Redirect("/swagger")).ExcludeFromDescription();

        // === BusinessAutomation API endpointy ===
        app.MapExpressionEngineEndpoints();

        // === EventDefinition Seeder API (pro E2E testy) ===
        app.MapEventDefinitionSeederEndpoints();
        app.MapSimpleEventSeederEndpoints();

        // === Diagnostické endpointy (pouze pro development) ===
        if (app.Environment.IsDevelopment())
        {
            app.UseDiagnosticEndpoints();
        }

        // === Automatická registrace všech CRUD endpointů ===
        app.MapAllEntityEndpoints(includeSpecifications: true);

        return app;
    }
}
