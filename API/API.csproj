<Project Sdk="Microsoft.NET.Sdk.Web">

    <PropertyGroup>
        <TargetFramework>net9.0</TargetFramework>
        <Nullable>enable</Nullable>
        <ImplicitUsings>enable</ImplicitUsings>
        <OpenApiGenerateDocuments>true</OpenApiGenerateDocuments>
        <UserSecretsId>aspnet-API-c66f86e6-59d1-498d-a02f-ef697992e7ab</UserSecretsId>
    </PropertyGroup>

    <ItemGroup>
        <PackageReference Include="Microsoft.AspNetCore.Authentication.JwtBearer" Version="9.0.7" NoWarn="NU1605" />
        <PackageReference Include="Microsoft.AspNetCore.Authentication.OpenIdConnect" Version="9.0.7" NoWarn="NU1605" />
        <PackageReference Include="Microsoft.AspNetCore.OpenApi" Version="9.0.7" />
        <PackageReference Include="Microsoft.EntityFrameworkCore.Design" Version="9.0.7">
          <IncludeAssets>runtime; build; native; contentfiles; analyzers; buildtransitive</IncludeAssets>
          <PrivateAssets>all</PrivateAssets>
        </PackageReference>
        <PackageReference Include="Microsoft.EntityFrameworkCore.InMemory" Version="9.0.7" />
        <PackageReference Include="Microsoft.Identity.Web" Version="3.10.0" />
        <PackageReference Include="Microsoft.Identity.Web.DownstreamApi" Version="3.10.0" />
        <PackageReference Include="Scalar.AspNetCore" Version="2.6.7" />
        <PackageReference Include="Swashbuckle.AspNetCore" Version="9.0.3" />
    </ItemGroup>

    <ItemGroup>
      <ProjectReference Include="..\Application\Application.csproj" />
      <ProjectReference Include="..\Infrastructure\Infrastructure.csproj" />
      <ProjectReference Include="..\BusinessAutomation\BusinessAutomation.Core.Abstractions\BusinessAutomation.Core.Abstractions.csproj" />
      <ProjectReference Include="..\BusinessAutomation\BusinessAutomation.Core.Infrastructure\BusinessAutomation.Core.Infrastructure.csproj" />
    </ItemGroup>
</Project>
