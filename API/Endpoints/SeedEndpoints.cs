using BusinessAutomation.Core.Abstractions.ExpressionEngine.Models;
using BusinessAutomation.Core.Abstractions.Database;
using Domain.Entities;
using Infrastructure.Persistence;
using Infrastructure.Persistence.Seeders;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;

namespace API.Endpoints;

/// <summary>
/// Minimal API endpointy pro naplnění databáze testovacími daty.
/// </summary>
public static class SeedEndpoints
{
    /// <summary>
    /// Registruje všechny seed endpointy.
    /// </summary>
    public static IEndpointRouteBuilder MapSeedEndpoints(this IEndpointRouteBuilder app)
    {
        var group = app.MapGroup("/api/seed")
            .WithTags("Seed")
            .WithOpenApi();

        // GET /api/seed/stats - Získá statistiky databáze
        group.MapGet("/stats", async (
            [FromServices] ApplicationDbContext context,
            [FromServices] IBusinessAutomationDbContext businessContext) =>
        {
            try
            {
                var stats = new
                {
                    Orders = await context.Set<Order>().CountAsync(),
                    OrderItems = await context.Set<OrderItem>().CountAsync(),
                    Invoices = await context.Set<Invoice>().CountAsync(),
                    InvoiceItems = await context.Set<InvoiceItem>().CountAsync(),
                    Expressions = await businessContext.Expressions.CountAsync()
                };

                return Results.Ok(stats);
            }
            catch (Exception ex)
            {
                return Results.Problem($"Chyba při načítání statistik: {ex.Message}");
            }
        })
        .WithName("GetDatabaseStats")
        .WithSummary("Získá statistiky databáze")
        .WithDescription("Vrací počet záznamů v jednotlivých tabulkách")
        .Produces<object>(StatusCodes.Status200OK)
        .Produces(StatusCodes.Status500InternalServerError);

        // POST /api/seed/orders-and-invoices - Naplní databázi objednávkami a fakturami
        group.MapPost("/orders-and-invoices", async (
            [FromServices] ApplicationDbContext context) =>
        {
            try
            {
                await OrderAndInvoiceSeeder.SeedAsync(context);
                return Results.Ok(new { Message = "Testovací objednávky a faktury byly úspěšně vytvořeny." });
            }
            catch (Exception ex)
            {
                return Results.BadRequest(new { Message = "Chyba při vytváření testovacích dat.", Error = ex.Message });
            }
        })
        .WithName("SeedOrdersAndInvoices")
        .WithSummary("Naplní databázi testovacími objednávkami a fakturami")
        .WithDescription("Vytvoří ukázkové objednávky s položkami a související faktury")
        .Produces<object>(StatusCodes.Status200OK)
        .Produces<object>(StatusCodes.Status400BadRequest);

        // POST /api/seed/business-rules - Vytvoří ukázková obchodní pravidla
        group.MapPost("/business-rules", async (
            [FromServices] IBusinessAutomationDbContext context) =>
        {
            try
            {
                // Zkontrolujeme, zda už existují pravidla
                if (await context.Expressions.AnyAsync())
                {
                    return Results.Ok(new { Message = "Obchodní pravidla již existují." });
                }

                var expressions = CreateSampleExpressions();
                await context.Expressions.AddRangeAsync(expressions);
                await context.SaveChangesAsync();

                return Results.Ok(new { Message = $"Bylo vytvořeno {expressions.Count} ukázkových výrazů." });
            }
            catch (Exception ex)
            {
                return Results.BadRequest(new { Message = "Chyba při vytváření obchodních pravidel.", Error = ex.Message });
            }
        })
        .WithName("SeedBusinessRules")
        .WithSummary("Vytvoří ukázková obchodní pravidla")
        .WithDescription("Vytvoří sadu ukázkových obchodních pravidel pro demonstraci funkcionality")
        .Produces<object>(StatusCodes.Status200OK)
        .Produces<object>(StatusCodes.Status400BadRequest);

        // POST /api/seed/all - Naplní databázi všemi testovacími daty
        group.MapPost("/all", async (
            [FromServices] ApplicationDbContext context,
            [FromServices] IBusinessAutomationDbContext businessContext) =>
        {
            try
            {
                var results = new List<string>();

                // Objednávky a faktury
                try
                {
                    await OrderAndInvoiceSeeder.SeedAsync(context);
                    results.Add("✅ Objednávky a faktury vytvořeny");
                }
                catch (Exception ex)
                {
                    results.Add($"❌ Chyba při vytváření objednávek: {ex.Message}");
                }

                // Obchodní pravidla
                try
                {
                    if (!await businessContext.Expressions.AnyAsync())
                    {
                        var expressions = CreateSampleExpressions();
                        await businessContext.Expressions.AddRangeAsync(expressions);
                        await businessContext.SaveChangesAsync();
                        results.Add($"✅ Vytvořeno {expressions.Count} výrazů");
                    }
                    else
                    {
                        results.Add("ℹ️ Obchodní pravidla již existují");
                    }
                }
                catch (Exception ex)
                {
                    results.Add($"❌ Chyba při vytváření pravidel: {ex.Message}");
                }

                return Results.Ok(new { Message = "Seed operace dokončena", Results = results });
            }
            catch (Exception ex)
            {
                return Results.Problem($"Chyba při seed operaci: {ex.Message}");
            }
        })
        .WithName("SeedAll")
        .WithSummary("Naplní databázi všemi testovacími daty")
        .WithDescription("Vytvoří všechna testovací data najednou - objednávky, faktury i obchodní pravidla")
        .Produces<object>(StatusCodes.Status200OK)
        .Produces(StatusCodes.Status500InternalServerError);

        // DELETE /api/seed/clear - Vymaže všechna testovací data
        group.MapDelete("/clear", async (
            [FromServices] ApplicationDbContext context,
            [FromServices] IBusinessAutomationDbContext businessContext) =>
        {
            try
            {
                var results = new List<string>();

                // Smazání v správném pořadí kvůli foreign key constraints
                var invoiceItemsCount = await context.Set<InvoiceItem>().CountAsync();
                if (invoiceItemsCount > 0)
                {
                    context.Set<InvoiceItem>().RemoveRange(context.Set<InvoiceItem>());
                    results.Add($"Smazáno {invoiceItemsCount} položek faktur");
                }

                var invoicesCount = await context.Set<Invoice>().CountAsync();
                if (invoicesCount > 0)
                {
                    context.Set<Invoice>().RemoveRange(context.Set<Invoice>());
                    results.Add($"Smazáno {invoicesCount} faktur");
                }

                var orderItemsCount = await context.Set<OrderItem>().CountAsync();
                if (orderItemsCount > 0)
                {
                    context.Set<OrderItem>().RemoveRange(context.Set<OrderItem>());
                    results.Add($"Smazáno {orderItemsCount} položek objednávek");
                }

                var ordersCount = await context.Set<Order>().CountAsync();
                if (ordersCount > 0)
                {
                    context.Set<Order>().RemoveRange(context.Set<Order>());
                    results.Add($"Smazáno {ordersCount} objednávek");
                }

                var rulesCount = await businessContext.Expressions.CountAsync();
                if (rulesCount > 0)
                {
                    businessContext.Expressions.RemoveRange(businessContext.Expressions);
                    results.Add($"Smazáno {rulesCount} obchodních pravidel");
                }

                await context.SaveChangesAsync();
                await businessContext.SaveChangesAsync();

                return Results.Ok(new { Message = "Všechna testovací data byla smazána", Results = results });
            }
            catch (Exception ex)
            {
                return Results.Problem($"Chyba při mazání dat: {ex.Message}");
            }
        })
        .WithName("ClearAllData")
        .WithSummary("Vymaže všechna testovací data")
        .WithDescription("Smaže všechny objednávky, faktury a obchodní pravidla z databáze")
        .Produces<object>(StatusCodes.Status200OK)
        .Produces(StatusCodes.Status500InternalServerError);

        // POST /api/seed/complex-rule - Vytvoří komplexní obchodní pravidlo
        group.MapPost("/complex-rule", async (
            [FromServices] IBusinessAutomationDbContext context) =>
        {
            try
            {
                var complexExpression = CreateComplexCustomerLoyaltyExpression();
                await context.Expressions.AddAsync(complexExpression);
                await context.SaveChangesAsync();

                return Results.Ok(new {
                    Message = "Komplexní výraz pro věrnostní slevy byl vytvořen.",
                    ExpressionId = complexExpression.Id,
                    ExpressionName = complexExpression.Name
                });
            }
            catch (Exception ex)
            {
                return Results.BadRequest(new { Message = "Chyba při vytváření komplexního pravidla.", Error = ex.Message });
            }
        })
        .WithName("CreateComplexRule")
        .WithSummary("Vytvoří komplexní pravidlo pro věrnostní slevy")
        .WithDescription("Vytvoří pravidlo, které počítá slevu podle celkové hodnoty faktur zákazníka za poslední rok")
        .Produces<object>(StatusCodes.Status200OK)
        .Produces<object>(StatusCodes.Status400BadRequest);

        return app;
    }

    /// <summary>
    /// Vytvoří ukázkové výrazy.
    /// </summary>
    private static List<Expression> CreateSampleExpressions()
    {
        var expressions = new List<Expression>();

        // Jednoduchý výraz pro kontrolu věku
        expressions.Add(new Expression
        {
            Id = Guid.NewGuid(),
            Name = "Kontrola věku 18+",
            Description = "Ověřuje, zda je osoba starší 18 let",
            ExpressionTree = new OperationNode
            {
                Operator = OperatorType.GreaterThanOrEqual,
                Operands = new List<ExpressionNode>
                {
                    new SourceValueNode { SourcePath = "Age" },
                    new ConstantNode { Value = "18", DataType = BusinessAutomation.Core.Abstractions.ExpressionEngine.Models.ValueType.Integer }
                }
            },
            IsActive = true,
            Version = 1,
            EffectiveFrom = DateTime.UtcNow,
            SchemaVersion = "1.0"
        });

        // Výraz pro slevu na základě částky
        expressions.Add(new Expression
        {
            Id = Guid.NewGuid(),
            Name = "Sleva pro velké objednávky",
            Description = "Poskytuje 10% slevu pro objednávky nad 1000 Kč",
            ExpressionTree = new OperationNode
            {
                Operator = OperatorType.If,
                Operands = new List<ExpressionNode>
                {
                    // Podmínka
                    new OperationNode
                    {
                        Operator = OperatorType.GreaterThan,
                        Operands = new List<ExpressionNode>
                        {
                            new SourceValueNode { SourcePath = "TotalAmount" },
                            new ConstantNode { Value = "1000", DataType = BusinessAutomation.Core.Abstractions.ExpressionEngine.Models.ValueType.Decimal }
                        }
                    },
                    // True hodnota
                    new ConstantNode { Value = "10", DataType = BusinessAutomation.Core.Abstractions.ExpressionEngine.Models.ValueType.Decimal },
                    // False hodnota
                    new ConstantNode { Value = "0", DataType = BusinessAutomation.Core.Abstractions.ExpressionEngine.Models.ValueType.Decimal }
                }
            },
            IsActive = true,
            Version = 1,
            EffectiveFrom = DateTime.UtcNow,
            SchemaVersion = "1.0"
        });

        return expressions;
    }

    /// <summary>
    /// Vytvoří komplexní výraz pro věrnostní slevy.
    /// </summary>
    private static Expression CreateComplexCustomerLoyaltyExpression()
    {
        return new Expression
        {
            Id = Guid.NewGuid(),
            Name = "Komplexní věrnostní sleva",
            Description = "Poskytuje slevu na základě věrnostních bodů",
            ExpressionTree = new OperationNode
            {
                Operator = OperatorType.If,
                Operands = new List<ExpressionNode>
                {
                    // Podmínka
                    new OperationNode
                    {
                        Operator = OperatorType.GreaterThanOrEqual,
                        Operands = new List<ExpressionNode>
                        {
                            new SourceValueNode { SourcePath = "LoyaltyPoints" },
                            new ConstantNode { Value = "1000", DataType = BusinessAutomation.Core.Abstractions.ExpressionEngine.Models.ValueType.Integer }
                        }
                    },
                    // True hodnota
                    new ConstantNode { Value = "15", DataType = BusinessAutomation.Core.Abstractions.ExpressionEngine.Models.ValueType.Decimal },
                    // False hodnota
                    new ConstantNode { Value = "0", DataType = BusinessAutomation.Core.Abstractions.ExpressionEngine.Models.ValueType.Decimal }
                }
            },
            IsActive = true,
            Version = 1,
            EffectiveFrom = DateTime.UtcNow,
            SchemaVersion = "1.0",
            InternalNotes = "Zjednodušený výraz pro věrnostní slevy"
        };
    }
}
