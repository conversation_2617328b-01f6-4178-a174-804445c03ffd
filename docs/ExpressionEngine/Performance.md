# Výkon a optimalizace

Tato dokumentace popisuje výkonnostní charakteristiky ExpressionEngine a techniky pro optimalizaci.

## 🚀 Výkonnostní charakteristiky

### Kompilace výrazů

ExpressionEngine kompiluje JSON výrazy do .NET Expression Trees, které jsou následně kompilovány do nativního kódu.

```
JSON → ExpressionNode → Expression Tree → Compiled Delegate → Execution
```

**Výhody:**
- ✅ Vysoký výkon při opakovaném vykonávání
- ✅ Nativní rychlost po kompilaci
- ✅ Type safety při runtime

**Nevýhody:**
- ⚠️ Pomalá první kompilace
- ⚠️ Paměťová náročnost při velkém počtu výrazů

### Benchmark výsledky

```
| Operace                    | Čas (ms) | Paměť (KB) |
|----------------------------|----------|------------|
| První kompilace            | 15-50    | 50-200     |
| Opakované vykonání         | 0.01-0.1 | 1-5        |
| Cache hit                  | 0.001    | <1         |
| JSON deserializace         | 1-5      | 10-50      |
| Lookup operace             | 5-20     | 20-100     |
| Agregace (1000 položek)    | 2-10     | 15-75      |
```

## 🎯 Caching strategie

### Expression Cache

ExpressionEngine automaticky cachuje zkompilované výrazy.

```csharp
public class ExpressionCacheService : IExpressionCacheService
{
    private readonly MemoryCache _cache;
    private readonly ExpressionCacheStatistics _statistics;

    public void Set<T>(string key, T value, TimeSpan expiration)
    {
        _cache.Set(key, value, expiration);
    }

    public T? Get<T>(string key)
    {
        if (_cache.TryGetValue(key, out var value))
        {
            _statistics.HitCount++;
            return (T)value;
        }
        
        _statistics.MissCount++;
        return default;
    }
}
```

### Cache klíče

Cache klíče se generují na základě:
- Hash ExpressionTree JSON
- Typ entity
- Verze výrazu

```csharp
private string GenerateCacheKey(Expression expression, Type entityType)
{
    var jsonHash = expression.ExpressionTree.GetHashCode();
    return $"expr_{jsonHash}_{entityType.Name}_{expression.Version}";
}
```

### Cache konfigurace

```csharp
// Výchozí nastavení
services.Configure<MemoryCacheOptions>(options =>
{
    options.SizeLimit = 1000; // Max 1000 výrazů
    options.CompactionPercentage = 0.25; // Uvolni 25% při dosažení limitu
});

// Vlastní expirační politika
services.AddSingleton<IExpressionCacheService>(provider =>
{
    return new ExpressionCacheService(
        defaultExpiration: TimeSpan.FromHours(1),
        maxSize: 500
    );
});
```

## 📊 Monitoring a metriky

### Cache statistiky

```csharp
public class ExpressionCacheStatistics
{
    public int HitCount { get; set; }
    public int MissCount { get; set; }
    public int TotalRequests => HitCount + MissCount;
    public double HitRatio => TotalRequests > 0 ? (double)HitCount / TotalRequests : 0;
    public int CachedItemsCount { get; set; }
    public long TotalMemoryUsage { get; set; }
}

// Použití
var stats = cacheService.GetStatistics();
logger.LogInformation($"Cache hit ratio: {stats.HitRatio:P2}");
logger.LogInformation($"Cached items: {stats.CachedItemsCount}");
```

### Performance logging

```csharp
public class PerformanceLoggingCalculationEngine : CalculationEngine
{
    private readonly ILogger<PerformanceLoggingCalculationEngine> _logger;

    public override async Task<object> ExecuteAsync(Expression expression, object entity)
    {
        var stopwatch = Stopwatch.StartNew();
        
        try
        {
            var result = await base.ExecuteAsync(expression, entity);
            
            stopwatch.Stop();
            _logger.LogDebug(
                "Expression {ExpressionName} executed in {ElapsedMs}ms",
                expression.Name,
                stopwatch.ElapsedMilliseconds
            );
            
            return result;
        }
        catch (Exception ex)
        {
            stopwatch.Stop();
            _logger.LogError(ex,
                "Expression {ExpressionName} failed after {ElapsedMs}ms",
                expression.Name,
                stopwatch.ElapsedMilliseconds
            );
            throw;
        }
    }
}
```

## 🔧 Optimalizační techniky

### 1. Předkompilace výrazů

```csharp
public class ExpressionPrecompiler : IHostedService
{
    private readonly ExpressionRepository _repository;
    private readonly CalculationEngine _engine;

    public async Task StartAsync(CancellationToken cancellationToken)
    {
        // Předkompiluj všechny aktivní výrazy při startu
        var activeExpressions = await _repository.GetActiveExpressionsAsync();
        
        foreach (var expression in activeExpressions)
        {
            try
            {
                // Dummy entity pro kompilaci
                var dummyEntity = CreateDummyEntity(expression);
                await _engine.ExecuteAsync(expression, dummyEntity);
                
                _logger.LogInformation(
                    "Precompiled expression: {ExpressionName}",
                    expression.Name
                );
            }
            catch (Exception ex)
            {
                _logger.LogWarning(ex,
                    "Failed to precompile expression: {ExpressionName}",
                    expression.Name
                );
            }
        }
    }
}

// Registrace
services.AddHostedService<ExpressionPrecompiler>();
```

### 2. Batch vykonávání

```csharp
public class BatchCalculationEngine
{
    public async Task<Dictionary<Guid, object>> ExecuteBatchAsync<T>(
        Expression expression,
        IEnumerable<T> entities)
    {
        var results = new Dictionary<Guid, object>();
        var compiledExpression = await GetCompiledExpressionAsync(expression, typeof(T));
        
        // Paralelní vykonávání
        var tasks = entities.Select(async entity =>
        {
            var entityId = GetEntityId(entity);
            var result = compiledExpression(entity);
            return new { EntityId = entityId, Result = result };
        });
        
        var batchResults = await Task.WhenAll(tasks);
        
        foreach (var item in batchResults)
        {
            results[item.EntityId] = item.Result;
        }
        
        return results;
    }
}
```

### 3. Lazy loading pro Lookup

```csharp
public class OptimizedLookupExpressionBuilder : ILookupExpressionBuilder
{
    public System.Linq.Expressions.Expression Build(
        LookupNode node,
        ParameterExpression parameter,
        Func<ExpressionNode, ParameterExpression, System.Linq.Expressions.Expression> buildChild)
    {
        // Vytvoř lazy loader místo okamžitého dotazu
        var lazyLoaderExpression = CreateLazyLoader(node, parameter, buildChild);
        return lazyLoaderExpression;
    }

    private System.Linq.Expressions.Expression CreateLazyLoader(
        LookupNode node,
        ParameterExpression parameter,
        Func<ExpressionNode, ParameterExpression, System.Linq.Expressions.Expression> buildChild)
    {
        // Implementace lazy loading logiky
        // Dotaz se vykoná pouze při prvním přístupu k hodnotě
        return System.Linq.Expressions.Expression.Call(
            typeof(LazyLookupHelper),
            nameof(LazyLookupHelper.GetValue),
            Type.EmptyTypes,
            System.Linq.Expressions.Expression.Constant(node),
            parameter
        );
    }
}
```

### 4. Connection pooling pro databázi

```csharp
// V appsettings.json
{
  "ConnectionStrings": {
    "DefaultConnection": "Data Source=database.db;Pooling=true;Max Pool Size=100;Min Pool Size=5;"
  }
}

// Konfigurace
services.AddDbContext<ApplicationDbContext>(options =>
{
    options.UseSqlite(connectionString, sqliteOptions =>
    {
        sqliteOptions.CommandTimeout(30);
    });
    
    // Povolení connection pooling
    options.EnableServiceProviderCaching();
    options.EnableSensitiveDataLogging(false);
});
```

## 📈 Škálování

### Horizontální škálování

```csharp
// Distributed cache pro multi-instance prostředí
services.AddStackExchangeRedisCache(options =>
{
    options.Configuration = "localhost:6379";
    options.InstanceName = "ExpressionEngine";
});

public class DistributedExpressionCacheService : IExpressionCacheService
{
    private readonly IDistributedCache _distributedCache;
    private readonly IMemoryCache _localCache;

    public T? Get<T>(string key)
    {
        // Nejdříve zkus local cache
        if (_localCache.TryGetValue(key, out var localValue))
        {
            return (T)localValue;
        }

        // Pak zkus distributed cache
        var distributedValue = _distributedCache.GetString(key);
        if (distributedValue != null)
        {
            var deserializedValue = JsonSerializer.Deserialize<T>(distributedValue);
            
            // Ulož do local cache pro rychlejší přístup
            _localCache.Set(key, deserializedValue, TimeSpan.FromMinutes(5));
            
            return deserializedValue;
        }

        return default;
    }
}
```

### Vertikální škálování

```csharp
// Konfigurace thread pool pro paralelní vykonávání
services.Configure<ThreadPoolSettings>(options =>
{
    options.MinWorkerThreads = Environment.ProcessorCount * 2;
    options.MaxWorkerThreads = Environment.ProcessorCount * 10;
});

// Paralelní vykonávání výrazů
public async Task<IEnumerable<TResult>> ExecuteParallelAsync<TEntity, TResult>(
    Expression expression,
    IEnumerable<TEntity> entities,
    int maxDegreeOfParallelism = -1)
{
    var parallelOptions = new ParallelQuery<TEntity>(entities);
    
    if (maxDegreeOfParallelism > 0)
    {
        parallelOptions = parallelOptions.WithDegreeOfParallelism(maxDegreeOfParallelism);
    }

    return parallelOptions
        .Select(entity => _calculationEngine.Execute<TResult>(expression, entity))
        .ToList();
}
```

## 🎯 Best Practices

### 1. Optimalizace výrazů

```csharp
// ✅ Dobré: Jednoduché podmínky první
{
  "NodeType": "Operation",
  "Operator": "And",
  "Operands": [
    {
      "NodeType": "SourceValue",
      "SourcePath": "IsActive"  // Rychlá boolean kontrola
    },
    {
      "NodeType": "Lookup",     // Pomalá databázová operace
      "TargetEntityName": "Customer",
      "ReturnFieldPath": "Type",
      "Condition": {...}
    }
  ]
}

// ❌ Špatné: Pomalé operace první
{
  "NodeType": "Operation",
  "Operator": "And",
  "Operands": [
    {
      "NodeType": "Lookup",     // Pomalá operace první
      "TargetEntityName": "Customer",
      "ReturnFieldPath": "Type",
      "Condition": {...}
    },
    {
      "NodeType": "SourceValue",
      "SourcePath": "IsActive"  // Rychlá kontrola až druhá
    }
  ]
}
```

### 2. Cache warming

```csharp
public class CacheWarmupService : IHostedService
{
    public async Task StartAsync(CancellationToken cancellationToken)
    {
        // Nahřej nejčastěji používané výrazy
        var popularExpressions = await GetPopularExpressionsAsync();
        
        foreach (var expression in popularExpressions)
        {
            await WarmupExpressionAsync(expression);
        }
    }

    private async Task WarmupExpressionAsync(Expression expression)
    {
        // Vytvoř dummy entity pro všechny podporované typy
        var entityTypes = GetSupportedEntityTypes();
        
        foreach (var entityType in entityTypes)
        {
            var dummyEntity = CreateDummyEntity(entityType);
            
            try
            {
                await _calculationEngine.ExecuteAsync(expression, dummyEntity);
            }
            catch
            {
                // Ignoruj chyby při warmup
            }
        }
    }
}
```

### 3. Memory management

```csharp
// Konfigurace garbage collection
services.Configure<GCSettings>(options =>
{
    // Server GC pro lepší výkon při vysokém zatížení
    options.IsServerGC = true;
    
    // Concurrent GC pro nižší latenci
    options.ConcurrentGC = true;
});

// Monitoring paměti
public class MemoryMonitoringService : BackgroundService
{
    protected override async Task ExecuteAsync(CancellationToken stoppingToken)
    {
        while (!stoppingToken.IsCancellationRequested)
        {
            var memoryUsage = GC.GetTotalMemory(false);
            var gen0Collections = GC.CollectionCount(0);
            var gen1Collections = GC.CollectionCount(1);
            var gen2Collections = GC.CollectionCount(2);

            _logger.LogInformation(
                "Memory: {MemoryMB}MB, GC: Gen0={Gen0}, Gen1={Gen1}, Gen2={Gen2}",
                memoryUsage / 1024 / 1024,
                gen0Collections,
                gen1Collections,
                gen2Collections
            );

            // Pokud je paměťové využití vysoké, vyčisti cache
            if (memoryUsage > 500 * 1024 * 1024) // 500MB
            {
                _cacheService.Clear();
                GC.Collect();
            }

            await Task.Delay(TimeSpan.FromMinutes(1), stoppingToken);
        }
    }
}
```

## 📊 Profiling a debugging

### Application Insights integrace

```csharp
services.AddApplicationInsightsTelemetry();

public class TelemetryCalculationEngine : CalculationEngine
{
    private readonly TelemetryClient _telemetryClient;

    public override async Task<object> ExecuteAsync(Expression expression, object entity)
    {
        using var operation = _telemetryClient.StartOperation<DependencyTelemetry>("ExpressionExecution");
        operation.Telemetry.Data = expression.Name;
        operation.Telemetry.Type = "ExpressionEngine";

        try
        {
            var result = await base.ExecuteAsync(expression, entity);
            operation.Telemetry.Success = true;
            return result;
        }
        catch (Exception ex)
        {
            operation.Telemetry.Success = false;
            _telemetryClient.TrackException(ex);
            throw;
        }
    }
}
```

### Custom performance counters

```csharp
public class PerformanceCounters
{
    private readonly Counter _expressionExecutions;
    private readonly Histogram _executionDuration;
    private readonly Gauge _cacheHitRatio;

    public PerformanceCounters(IMeterFactory meterFactory)
    {
        var meter = meterFactory.Create("ExpressionEngine");
        
        _expressionExecutions = meter.CreateCounter<long>(
            "expression_executions_total",
            "Total number of expression executions"
        );
        
        _executionDuration = meter.CreateHistogram<double>(
            "expression_execution_duration_ms",
            "Expression execution duration in milliseconds"
        );
        
        _cacheHitRatio = meter.CreateGauge<double>(
            "expression_cache_hit_ratio",
            "Expression cache hit ratio"
        );
    }

    public void RecordExecution(string expressionName, double durationMs, bool cacheHit)
    {
        _expressionExecutions.Add(1, new("expression_name", expressionName));
        _executionDuration.Record(durationMs, new("expression_name", expressionName));
        
        if (cacheHit)
        {
            _cacheHitRatio.Record(1.0);
        }
        else
        {
            _cacheHitRatio.Record(0.0);
        }
    }
}
```
