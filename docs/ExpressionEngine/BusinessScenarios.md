# Business scénáře a příklady

Tato dokumentace obsahuje reálné business scénáře a jejich implementaci pomocí ExpressionEngine.

## 🛒 E-commerce scénáře

### 1. Dynamické slevy

#### Scénář: VIP zákazník sleva
**Požadavek**: VIP zákazníci dostávají 15% slevu na objednávky nad 2000 Kč.

```json
{
  "Name": "VIP Customer Discount",
  "Description": "15% sleva pro VIP zákazníky nad 2000 Kč",
  "ExpressionTree": {
    "NodeType": "Operation",
    "Operator": "If",
    "Operands": [
      {
        "NodeType": "Operation",
        "Operator": "And",
        "Operands": [
          {
            "NodeType": "Operation",
            "Operator": "Equal",
            "Operands": [
              {
                "NodeType": "SourceValue",
                "SourcePath": "CustomerType"
              },
              {
                "NodeType": "Constant",
                "DataType": "String",
                "Value": "VIP"
              }
            ]
          },
          {
            "NodeType": "Operation",
            "Operator": "GreaterThan",
            "Operands": [
              {
                "NodeType": "SourceValue",
                "SourcePath": "TotalAmount"
              },
              {
                "NodeType": "Constant",
                "DataType": "Decimal",
                "Value": "2000"
              }
            ]
          }
        ]
      },
      {
        "NodeType": "Operation",
        "Operator": "Multiply",
        "Operands": [
          {
            "NodeType": "SourceValue",
            "SourcePath": "TotalAmount"
          },
          {
            "NodeType": "Constant",
            "DataType": "Decimal",
            "Value": "0.15"
          }
        ]
      },
      {
        "NodeType": "Constant",
        "DataType": "Decimal",
        "Value": "0"
      }
    ]
  }
}
```

#### Scénář: Množstevní sleva
**Požadavek**: Sleva na základě počtu položek: 5% za 5+ položek, 10% za 10+ položek.

```json
{
  "Name": "Quantity Discount",
  "Description": "Množstevní sleva podle počtu položek",
  "ExpressionTree": {
    "NodeType": "Operation",
    "Operator": "If",
    "Operands": [
      {
        "NodeType": "Operation",
        "Operator": "GreaterThanOrEqual",
        "Operands": [
          {
            "NodeType": "Aggregation",
            "AggregationType": "Count",
            "CollectionPath": "Items"
          },
          {
            "NodeType": "Constant",
            "DataType": "Integer",
            "Value": "10"
          }
        ]
      },
      {
        "NodeType": "Operation",
        "Operator": "Multiply",
        "Operands": [
          {
            "NodeType": "SourceValue",
            "SourcePath": "TotalAmount"
          },
          {
            "NodeType": "Constant",
            "DataType": "Decimal",
            "Value": "0.1"
          }
        ]
      },
      {
        "NodeType": "Operation",
        "Operator": "If",
        "Operands": [
          {
            "NodeType": "Operation",
            "Operator": "GreaterThanOrEqual",
            "Operands": [
              {
                "NodeType": "Aggregation",
                "AggregationType": "Count",
                "CollectionPath": "Items"
              },
              {
                "NodeType": "Constant",
                "DataType": "Integer",
                "Value": "5"
              }
            ]
          },
          {
            "NodeType": "Operation",
            "Operator": "Multiply",
            "Operands": [
              {
                "NodeType": "SourceValue",
                "SourcePath": "TotalAmount"
              },
              {
                "NodeType": "Constant",
                "DataType": "Decimal",
                "Value": "0.05"
              }
            ]
          },
          {
            "NodeType": "Constant",
            "DataType": "Decimal",
            "Value": "0"
          }
        ]
      }
    ]
  }
}
```

### 2. Dopravné a poplatky

#### Scénář: Dynamické dopravné
**Požadavek**: Doprava zdarma nad 1500 Kč, jinak 150 Kč. VIP zákazníci mají dopravu vždy zdarma.

```json
{
  "Name": "Shipping Cost Calculation",
  "Description": "Výpočet dopravného s preferencí pro VIP",
  "ExpressionTree": {
    "NodeType": "Operation",
    "Operator": "If",
    "Operands": [
      {
        "NodeType": "Operation",
        "Operator": "Or",
        "Operands": [
          {
            "NodeType": "Operation",
            "Operator": "Equal",
            "Operands": [
              {
                "NodeType": "SourceValue",
                "SourcePath": "CustomerType"
              },
              {
                "NodeType": "Constant",
                "DataType": "String",
                "Value": "VIP"
              }
            ]
          },
          {
            "NodeType": "Operation",
            "Operator": "GreaterThanOrEqual",
            "Operands": [
              {
                "NodeType": "SourceValue",
                "SourcePath": "TotalAmount"
              },
              {
                "NodeType": "Constant",
                "DataType": "Decimal",
                "Value": "1500"
              }
            ]
          }
        ]
      },
      {
        "NodeType": "Constant",
        "DataType": "Decimal",
        "Value": "0"
      },
      {
        "NodeType": "Constant",
        "DataType": "Decimal",
        "Value": "150"
      }
    ]
  }
}
```

## 💰 Finanční scénáře

### 3. Úrokové sazby a poplatky

#### Scénář: Dynamická úroková sazba
**Požadavek**: Úroková sazba závisí na ratingu klienta a výši úvěru.

```json
{
  "Name": "Interest Rate Calculation",
  "Description": "Výpočet úrokové sazby podle ratingu a výše úvěru",
  "ExpressionTree": {
    "NodeType": "Operation",
    "Operator": "If",
    "Operands": [
      {
        "NodeType": "Operation",
        "Operator": "Equal",
        "Operands": [
          {
            "NodeType": "SourceValue",
            "SourcePath": "CreditRating"
          },
          {
            "NodeType": "Constant",
            "DataType": "String",
            "Value": "AAA"
          }
        ]
      },
      {
        "NodeType": "Operation",
        "Operator": "If",
        "Operands": [
          {
            "NodeType": "Operation",
            "Operator": "GreaterThan",
            "Operands": [
              {
                "NodeType": "SourceValue",
                "SourcePath": "LoanAmount"
              },
              {
                "NodeType": "Constant",
                "DataType": "Decimal",
                "Value": "1000000"
              }
            ]
          },
          {
            "NodeType": "Constant",
            "DataType": "Decimal",
            "Value": "2.5"
          },
          {
            "NodeType": "Constant",
            "DataType": "Decimal",
            "Value": "3.0"
          }
        ]
      },
      {
        "NodeType": "Operation",
        "Operator": "If",
        "Operands": [
          {
            "NodeType": "Operation",
            "Operator": "Equal",
            "Operands": [
              {
                "NodeType": "SourceValue",
                "SourcePath": "CreditRating"
              },
              {
                "NodeType": "Constant",
                "DataType": "String",
                "Value": "AA"
              }
            ]
          },
          {
            "NodeType": "Constant",
            "DataType": "Decimal",
            "Value": "4.0"
          },
          {
            "NodeType": "Constant",
            "DataType": "Decimal",
            "Value": "5.5"
          }
        ]
      }
    ]
  }
}
```

### 4. Pojistné a rizikové hodnocení

#### Scénář: Výpočet pojistného
**Požadavek**: Pojistné závisí na věku, typu vozidla a historii škod.

```json
{
  "Name": "Insurance Premium Calculation",
  "Description": "Výpočet pojistného na základě rizikových faktorů",
  "ExpressionTree": {
    "NodeType": "Operation",
    "Operator": "Multiply",
    "Operands": [
      {
        "NodeType": "Constant",
        "DataType": "Decimal",
        "Value": "5000"
      },
      {
        "NodeType": "Operation",
        "Operator": "Multiply",
        "Operands": [
          {
            "NodeType": "Operation",
            "Operator": "If",
            "Operands": [
              {
                "NodeType": "Operation",
                "Operator": "LessThan",
                "Operands": [
                  {
                    "NodeType": "SourceValue",
                    "SourcePath": "DriverAge"
                  },
                  {
                    "NodeType": "Constant",
                    "DataType": "Integer",
                    "Value": "25"
                  }
                ]
              },
              {
                "NodeType": "Constant",
                "DataType": "Decimal",
                "Value": "1.5"
              },
              {
                "NodeType": "Constant",
                "DataType": "Decimal",
                "Value": "1.0"
              }
            ]
          },
          {
            "NodeType": "Operation",
            "Operator": "If",
            "Operands": [
              {
                "NodeType": "Operation",
                "Operator": "GreaterThan",
                "Operands": [
                  {
                    "NodeType": "Lookup",
                    "TargetEntityName": "Driver",
                    "ReturnFieldPath": "AccidentCount",
                    "Condition": {
                      "NodeType": "Operation",
                      "Operator": "Equal",
                      "Operands": [
                        {
                          "NodeType": "SourceValue",
                          "SourcePath": "Id"
                        },
                        {
                          "NodeType": "SourceValue",
                          "SourcePath": "DriverId"
                        }
                      ]
                    }
                  },
                  {
                    "NodeType": "Constant",
                    "DataType": "Integer",
                    "Value": "0"
                  }
                ]
              },
              {
                "NodeType": "Constant",
                "DataType": "Decimal",
                "Value": "1.3"
              },
              {
                "NodeType": "Constant",
                "DataType": "Decimal",
                "Value": "1.0"
              }
            ]
          }
        ]
      }
    ]
  }
}
```

## 🏢 HR a mzdové scénáře

### 5. Výpočet bonusů

#### Scénář: Výkonnostní bonus
**Požadavek**: Bonus závisí na dosažení cílů a délce zaměstnání.

```json
{
  "Name": "Performance Bonus Calculation",
  "Description": "Výpočet bonusu podle výkonu a senority",
  "ExpressionTree": {
    "NodeType": "Operation",
    "Operator": "Multiply",
    "Operands": [
      {
        "NodeType": "SourceValue",
        "SourcePath": "BaseSalary"
      },
      {
        "NodeType": "Operation",
        "Operator": "Multiply",
        "Operands": [
          {
            "NodeType": "Operation",
            "Operator": "Divide",
            "Operands": [
              {
                "NodeType": "SourceValue",
                "SourcePath": "PerformanceScore"
              },
              {
                "NodeType": "Constant",
                "DataType": "Decimal",
                "Value": "100"
              }
            ]
          },
          {
            "NodeType": "Operation",
            "Operator": "If",
            "Operands": [
              {
                "NodeType": "Operation",
                "Operator": "GreaterThan",
                "Operands": [
                  {
                    "NodeType": "SourceValue",
                    "SourcePath": "YearsOfService"
                  },
                  {
                    "NodeType": "Constant",
                    "DataType": "Integer",
                    "Value": "5"
                  }
                ]
              },
              {
                "NodeType": "Constant",
                "DataType": "Decimal",
                "Value": "0.2"
              },
              {
                "NodeType": "Constant",
                "DataType": "Decimal",
                "Value": "0.1"
              }
            ]
          }
        ]
      }
    ]
  }
}
```

## 🏭 Výrobní scénáře

### 6. Kontrola kvality

#### Scénář: Hodnocení kvality produktu
**Požadavek**: Produkt projde kontrolou kvality na základě více parametrů.

```json
{
  "Name": "Quality Control Check",
  "Description": "Kontrola kvality produktu podle stanovených kritérií",
  "ExpressionTree": {
    "NodeType": "Operation",
    "Operator": "And",
    "Operands": [
      {
        "NodeType": "Operation",
        "Operator": "And",
        "Operands": [
          {
            "NodeType": "Operation",
            "Operator": "GreaterThanOrEqual",
            "Operands": [
              {
                "NodeType": "SourceValue",
                "SourcePath": "DimensionAccuracy"
              },
              {
                "NodeType": "Constant",
                "DataType": "Decimal",
                "Value": "95.0"
              }
            ]
          },
          {
            "NodeType": "Operation",
            "Operator": "LessThanOrEqual",
            "Operands": [
              {
                "NodeType": "SourceValue",
                "SourcePath": "DefectCount"
              },
              {
                "NodeType": "Constant",
                "DataType": "Integer",
                "Value": "2"
              }
            ]
          }
        ]
      },
      {
        "NodeType": "Operation",
        "Operator": "And",
        "Operands": [
          {
            "NodeType": "Operation",
            "Operator": "GreaterThanOrEqual",
            "Operands": [
              {
                "NodeType": "SourceValue",
                "SourcePath": "MaterialQuality"
              },
              {
                "NodeType": "Constant",
                "DataType": "Decimal",
                "Value": "90.0"
              }
            ]
          },
          {
            "NodeType": "Operation",
            "Operator": "Equal",
            "Operands": [
              {
                "NodeType": "SourceValue",
                "SourcePath": "CertificationStatus"
              },
              {
                "NodeType": "Constant",
                "DataType": "String",
                "Value": "Approved"
              }
            ]
          }
        ]
      }
    ]
  }
}
```

## 🎯 Marketingové scénáře

### 7. Segmentace zákazníků

#### Scénář: Automatická kategorizace zákazníků
**Požadavek**: Zákazníci se automaticky kategorizují podle historie nákupů.

```json
{
  "Name": "Customer Segmentation",
  "Description": "Automatická kategorizace zákazníků podle nákupního chování",
  "ExpressionTree": {
    "NodeType": "Operation",
    "Operator": "If",
    "Operands": [
      {
        "NodeType": "Operation",
        "Operator": "And",
        "Operands": [
          {
            "NodeType": "Operation",
            "Operator": "GreaterThan",
            "Operands": [
              {
                "NodeType": "Lookup",
                "TargetEntityName": "Customer",
                "ReturnFieldPath": "TotalSpent",
                "Condition": {
                  "NodeType": "Operation",
                  "Operator": "Equal",
                  "Operands": [
                    {
                      "NodeType": "SourceValue",
                      "SourcePath": "Id"
                    },
                    {
                      "NodeType": "SourceValue",
                      "SourcePath": "CustomerId"
                    }
                  ]
                }
              },
              {
                "NodeType": "Constant",
                "DataType": "Decimal",
                "Value": "50000"
              }
            ]
          },
          {
            "NodeType": "Operation",
            "Operator": "GreaterThan",
            "Operands": [
              {
                "NodeType": "Lookup",
                "TargetEntityName": "Customer",
                "ReturnFieldPath": "OrderCount",
                "Condition": {
                  "NodeType": "Operation",
                  "Operator": "Equal",
                  "Operands": [
                    {
                      "NodeType": "SourceValue",
                      "SourcePath": "Id"
                    },
                    {
                      "NodeType": "SourceValue",
                      "SourcePath": "CustomerId"
                    }
                  ]
                }
              },
              {
                "NodeType": "Constant",
                "DataType": "Integer",
                "Value": "20"
              }
            ]
          }
        ]
      },
      {
        "NodeType": "Constant",
        "DataType": "String",
        "Value": "Premium"
      },
      {
        "NodeType": "Operation",
        "Operator": "If",
        "Operands": [
          {
            "NodeType": "Operation",
            "Operator": "GreaterThan",
            "Operands": [
              {
                "NodeType": "Lookup",
                "TargetEntityName": "Customer",
                "ReturnFieldPath": "TotalSpent",
                "Condition": {
                  "NodeType": "Operation",
                  "Operator": "Equal",
                  "Operands": [
                    {
                      "NodeType": "SourceValue",
                      "SourcePath": "Id"
                    },
                    {
                      "NodeType": "SourceValue",
                      "SourcePath": "CustomerId"
                    }
                  ]
                }
              },
              {
                "NodeType": "Constant",
                "DataType": "Decimal",
                "Value": "10000"
              }
            ]
          },
          {
            "NodeType": "Constant",
            "DataType": "String",
            "Value": "Gold"
          },
          {
            "NodeType": "Constant",
            "DataType": "String",
            "Value": "Standard"
          }
        ]
      }
    ]
  }
}
```

## 🔧 Implementace v kódu

### Použití v aplikaci

```csharp
public class BusinessRuleService
{
    private readonly CalculationEngine _calculationEngine;
    private readonly ExpressionRepository _expressionRepository;

    public async Task<decimal> CalculateDiscountAsync(Order order)
    {
        var discountExpression = await _expressionRepository
            .GetByNameAsync("VIP Customer Discount");
            
        if (discountExpression?.IsActive == true)
        {
            var discount = await _calculationEngine
                .ExecuteAsync(discountExpression, order);
            return Convert.ToDecimal(discount);
        }
        
        return 0m;
    }

    public async Task<string> CategorizeCustomerAsync(Order order)
    {
        var segmentationExpression = await _expressionRepository
            .GetByNameAsync("Customer Segmentation");
            
        if (segmentationExpression?.IsActive == true)
        {
            var category = await _calculationEngine
                .ExecuteAsync(segmentationExpression, order);
            return category?.ToString() ?? "Standard";
        }
        
        return "Standard";
    }
}
```

### Testování business pravidel

```csharp
[Test]
public async Task VipCustomerDiscount_ShouldCalculateCorrectly()
{
    // Arrange
    var order = new Order
    {
        CustomerType = "VIP",
        TotalAmount = 3000m
    };

    var expression = await CreateVipDiscountExpression();

    // Act
    var discount = await _calculationEngine.ExecuteAsync(expression, order);

    // Assert
    Assert.AreEqual(450m, Convert.ToDecimal(discount)); // 15% z 3000
}
```
