# ExpressionEngine - Dokumentační index

Kompletní dokumentace pro ExpressionEngine systém - pokročilý nástroj pro definování a vykonávání business pravidel v JSON formátu.

## 📋 Přehled dokumentace

### 🏠 [README.md](./README.md) - <PERSON><PERSON><PERSON><PERSON> p<PERSON>ehled
**Začněte zde!** Základn<PERSON> přehled systému, architektura, rych<PERSON><PERSON> start a konfigurace.

**Obsahuje:**
- 🎯 Přehled funkcionalit
- 🏗️ Architektura systému  
- 📋 Typy uzlů (stru<PERSON><PERSON><PERSON> přehled)
- 🚀 <PERSON><PERSON><PERSON><PERSON> start s příklady
- 🔧 Konfigurace DI

---

### 🧩 [NodeTypes.md](./NodeTypes.md) - Typy uzlů a operátorů
**Detailní reference všech typů ExpressionNode.**

**Obsahuje:**
- ✅ **ConstantNode** - konstant<PERSON><PERSON> hodnoty (Inte<PERSON>, Dec<PERSON><PERSON>, String, Boolean, DateTime)
- ✅ **SourceValueNode** - přístup k vlastnostem entity
- ✅ **OperationNode** - matematic<PERSON><PERSON>, logické a podmíněné operace
- ✅ **LookupNode** - vyhledání v jiných entitách
- ✅ **AggregationNode** - agregační funkce (Count, Sum, Average, Min, Max)
- 🔗 Kombinování uzlů do složitých výrazů

---

### 📄 [JsonFormat.md](./JsonFormat.md) - JSON formát a serializace
**Vše o JSON struktuře a serializaci.**

**Obsahuje:**
- 🏗️ Základní JSON struktura
- 📝 JSON Schema pro všechny typy uzlů
- 🔄 Serializace a deserializace
- 📋 Kompletní příklady JSON výrazů
- 🎯 Best practices pro JSON
- ⚠️ Časté chyby a jejich řešení
- 🔧 Debugging JSON

---

### 💼 [BusinessScenarios.md](./BusinessScenarios.md) - Business scénáře
**Reálné business případy implementované v ExpressionEngine.**

**Obsahuje:**
- 🛒 **E-commerce**: VIP slevy, množstevní slevy, dopravné
- 💰 **Finance**: úrokové sazby, pojistné, rizikové hodnocení
- 🏢 **HR**: výkonnostní bonusy, mzdové výpočty
- 🏭 **Výroba**: kontrola kvality, výrobní parametry
- 🎯 **Marketing**: segmentace zákazníků, targeting
- 🔧 Implementace v kódu
- 🧪 Testování business pravidel

---

### 🔧 [ApiReference.md](./ApiReference.md) - API dokumentace
**Kompletní API reference všech tříd a rozhraní.**

**Obsahuje:**
- 🏗️ **Základní komponenty**: CalculationEngine, ExpressionRepository
- 🧩 **ExpressionNode typy**: všechny třídy s vlastnostmi a metodami
- 🔧 **Builder rozhraní**: IExpressionBuilder a specializované buildery
- 💾 **Data Access**: IExpressionDataProvider
- 🚀 **Cache služby**: IExpressionCacheService
- 📊 **Enumerace**: ValueType, OperatorType, AggregationType
- 🔧 **Konfigurace**: DI registrace
- ⚠️ **Výjimky**: ExpressionExecutionException, NotFoundException
- 📝 **Příklady použití**

---

### ⚡ [Performance.md](./Performance.md) - Výkon a optimalizace
**Výkonnostní charakteristiky a optimalizační techniky.**

**Obsahuje:**
- 🚀 **Výkonnostní charakteristiky**: benchmark výsledky
- 🎯 **Caching strategie**: Expression cache, cache klíče, konfigurace
- 📊 **Monitoring**: cache statistiky, performance logging
- 🔧 **Optimalizační techniky**: předkompilace, batch vykonávání, lazy loading
- 📈 **Škálování**: horizontální a vertikální škálování
- 🎯 **Best Practices**: optimalizace výrazů, cache warming, memory management
- 📊 **Profiling**: Application Insights, custom counters

---

### 🧪 [Testing.md](./Testing.md) - Testování
**Kompletní strategie testování ExpressionEngine.**

**Obsahuje:**
- 🧪 **Typy testů**: Unit, Integration, JSON, Business scenario
- 🎯 **Test utilities**: builders, mock služby
- 🔍 **Test coverage**: pokrytí kódu, coverage cíle
- 🚀 **Performance testy**: load testing, memory testing
- 📊 **CI/CD**: GitHub Actions workflow, test kategorization
- 📝 **Příklady testů** pro všechny komponenty

---

## 🎯 Doporučené pořadí čtení

### 👨‍💻 Pro vývojáře (první seznámení)
1. **[README.md](./README.md)** - základní přehled a rychlý start
2. **[NodeTypes.md](./NodeTypes.md)** - pochopení typů uzlů
3. **[JsonFormat.md](./JsonFormat.md)** - práce s JSON formátem
4. **[ApiReference.md](./ApiReference.md)** - API dokumentace

### 💼 Pro business analytiky
1. **[README.md](./README.md)** - přehled možností
2. **[BusinessScenarios.md](./BusinessScenarios.md)** - reálné příklady
3. **[JsonFormat.md](./JsonFormat.md)** - tvorba JSON pravidel
4. **[NodeTypes.md](./NodeTypes.md)** - detaily typů uzlů

### 🏗️ Pro architekty a DevOps
1. **[README.md](./README.md)** - architektura systému
2. **[Performance.md](./Performance.md)** - výkon a škálování
3. **[ApiReference.md](./ApiReference.md)** - technické detaily
4. **[Testing.md](./Testing.md)** - testovací strategie

### 🧪 Pro testery
1. **[Testing.md](./Testing.md)** - testovací strategie
2. **[BusinessScenarios.md](./BusinessScenarios.md)** - test cases
3. **[JsonFormat.md](./JsonFormat.md)** - JSON validace
4. **[Performance.md](./Performance.md)** - performance testing

---

## 📊 Statistiky dokumentace

- **Celkem souborů**: 7
- **Celkem stránek**: ~50+ stránek obsahu
- **Příklady kódu**: 100+ ukázek
- **JSON příklady**: 50+ kompletních JSON výrazů
- **Business scénáře**: 15+ reálných případů
- **Test příklady**: 30+ testovacích případů

---

## 🔄 Aktualizace dokumentace

Dokumentace je udržována synchronně s kódem. Při změnách v ExpressionEngine:

1. **Aktualizujte příslušné MD soubory**
2. **Přidejte nové příklady** do BusinessScenarios.md
3. **Aktualizujte API reference** v ApiReference.md
4. **Přidejte testy** a dokumentujte je v Testing.md
5. **Aktualizujte performance benchmarky** v Performance.md

---

## 📞 Podpora

Pro otázky k dokumentaci nebo ExpressionEngine systému:

- 📧 **Email**: <EMAIL>
- 💬 **Teams**: ExpressionEngine kanál
- 🐛 **Issues**: GitHub repository
- 📖 **Wiki**: Interní wiki stránky

---

**Poslední aktualizace**: 2024-08-03  
**Verze dokumentace**: 1.0  
**Kompatibilní s**: ExpressionEngine v1.0+
