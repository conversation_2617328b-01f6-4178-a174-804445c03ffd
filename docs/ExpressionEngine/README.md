# ExpressionEngine - Dokumentace

ExpressionEngine je pokročilý systém pro definování, ukládání a vykonávání business pravidel a výpočtů v JSON formátu. Umožňuje dynamické vytváření složitých logických a matematických výrazů bez nutnosti kompilace kódu.

## 🎯 Přehled

ExpressionEngine poskytuje:
- **JSON-driven konfiguraci** - výrazy se definují v JSON formátu
- **Dynamické vykonávání** - výrazy se vykonávají za běhu aplikace
- **Cross-entity reference** - možnost odkazovat na data z jiných entit
- **Agregační funkce** - počítání, suma, průměr, min/max
- **Caching** - automatické cachování výsledků pro výkon
- **Validace** - kontrola syntaxe a sémantiky výrazů

## 🏗️ Architektura

```
┌─────────────────┐    ┌──────────────────┐    ┌─────────────────┐
│   JSON Input    │───▶│ ExpressionEngine │───▶│    Result       │
│                 │    │                  │    │                 │
│ {               │    │ ┌──────────────┐ │    │ 42              │
│   "NodeType":   │    │ │ JSON Parser  │ │    │ "Premium"       │
│   "Constant",   │    │ └──────────────┘ │    │ true            │
│   "Value": "42" │    │ ┌──────────────┐ │    │ 1500.50         │
│ }               │    │ │ Calculation  │ │    │                 │
└─────────────────┘    │ │ Engine       │ │    └─────────────────┘
                       │ └──────────────┘ │
                       │ ┌──────────────┐ │
                       │ │ Data Access  │ │
                       │ └──────────────┘ │
                       └──────────────────┘
```

## 📋 Typy uzlů (ExpressionNode)

### 1. **ConstantNode** - Konstantní hodnoty
```json
{
  "NodeType": "Constant",
  "DataType": "Integer|Decimal|String|Boolean|DateTime",
  "Value": "42"
}
```

### 2. **SourceValueNode** - Přístup k vlastnostem entity
```json
{
  "NodeType": "SourceValue",
  "SourcePath": "Name"
}
```

### 3. **OperationNode** - Logické a matematické operace
```json
{
  "NodeType": "Operation",
  "Operator": "Equal|Add|If|And|Or|...",
  "Operands": [...]
}
```

### 4. **LookupNode** - Vyhledání v jiné entitě
```json
{
  "NodeType": "Lookup",
  "TargetEntityName": "Order",
  "ReturnFieldPath": "TotalAmount",
  "Condition": {...}
}
```

### 5. **AggregationNode** - Agregační funkce
```json
{
  "NodeType": "Aggregation",
  "AggregationType": "Count|Sum|Average|Min|Max",
  "CollectionPath": "Items"
}
```

## 🚀 Rychlý start

### 1. Základní použití

```csharp
// Vytvoření výrazu
var expression = new Expression
{
    Id = Guid.NewGuid(),
    Name = "Sleva pro VIP zákazníky",
    Description = "10% sleva pro objednávky nad 1000 Kč",
    ExpressionTree = new OperationNode
    {
        Operator = OperatorType.If,
        Operands = new List<ExpressionNode>
        {
            // Podmínka: TotalAmount > 1000
            new OperationNode
            {
                Operator = OperatorType.GreaterThan,
                Operands = new List<ExpressionNode>
                {
                    new SourceValueNode { SourcePath = "TotalAmount" },
                    new ConstantNode { DataType = ValueType.Decimal, Value = "1000" }
                }
            },
            // True: 10% sleva
            new OperationNode
            {
                Operator = OperatorType.Multiply,
                Operands = new List<ExpressionNode>
                {
                    new SourceValueNode { SourcePath = "TotalAmount" },
                    new ConstantNode { DataType = ValueType.Decimal, Value = "0.1" }
                }
            },
            // False: žádná sleva
            new ConstantNode { DataType = ValueType.Decimal, Value = "0" }
        }
    },
    IsActive = true,
    Version = 1,
    EffectiveFrom = DateTime.UtcNow,
    SchemaVersion = "1.0"
};

// Uložení výrazu
await expressionRepository.AddAsync(expression);

// Vykonání výrazu
var order = new Order { TotalAmount = 1500m };
var discount = await calculationEngine.ExecuteAsync(expression, order);
// Výsledek: 150 (10% z 1500)
```

### 2. JSON definice

```json
{
  "NodeType": "Operation",
  "Operator": "If",
  "Operands": [
    {
      "NodeType": "Operation",
      "Operator": "GreaterThan",
      "Operands": [
        {
          "NodeType": "SourceValue",
          "SourcePath": "TotalAmount"
        },
        {
          "NodeType": "Constant",
          "DataType": "Decimal",
          "Value": "1000"
        }
      ]
    },
    {
      "NodeType": "Operation",
      "Operator": "Multiply",
      "Operands": [
        {
          "NodeType": "SourceValue",
          "SourcePath": "TotalAmount"
        },
        {
          "NodeType": "Constant",
          "DataType": "Decimal",
          "Value": "0.1"
        }
      ]
    },
    {
      "NodeType": "Constant",
      "DataType": "Decimal",
      "Value": "0"
    }
  ]
}
```

## 📚 Detailní dokumentace

### 🧩 [Typy uzlů a operátorů](./NodeTypes.md)
Kompletní popis všech typů ExpressionNode včetně JSON formátu a příkladů použití.

### 📄 [JSON formát a serializace](./JsonFormat.md)
Detailní dokumentace JSON struktury, serializace/deserializace a best practices.

### 💼 [Business scénáře a příklady](./BusinessScenarios.md)
Reálné business případy implementované pomocí ExpressionEngine - slevy, pojistné, bonusy, atd.

### 🔧 [API reference](./ApiReference.md)
Kompletní API dokumentace všech tříd, rozhraní a metod ExpressionEngine systému.

### ⚡ [Výkon a optimalizace](./Performance.md)
Výkonnostní charakteristiky, caching strategie, optimalizační techniky a škálování.

### 🧪 [Testování](./Testing.md)
Strategie testování, test utilities, performance testy a CI/CD integrace.

## 🔧 Konfigurace

### Dependency Injection

```csharp
// V Program.cs nebo Startup.cs
services.AddScoped<CalculationEngine>();
services.AddScoped<ExpressionRepository>();
services.AddScoped<IExpressionBuilder, ExpressionBuilder>();
services.AddScoped<IExpressionDataProvider, ExpressionDataProvider>();

// Registrace builderů
services.AddScoped<IConstantExpressionBuilder, ConstantExpressionBuilder>();
services.AddScoped<ISourceExpressionBuilder, SourceExpressionBuilder>();
services.AddScoped<IOperationExpressionBuilder, OperationExpressionBuilder>();
services.AddScoped<ILookupExpressionBuilder, LookupExpressionBuilder>();
services.AddScoped<IAggregationExpressionBuilder, AggregationExpressionBuilder>();

// Cache služby
services.AddSingleton<IExpressionCacheService, ExpressionCacheService>();
```

### Entity Type Mapping

```csharp
services.AddSingleton<IReadOnlyDictionary<string, Type>>(provider =>
{
    return new Dictionary<string, Type>
    {
        { "Order", typeof(Order) },
        { "OrderItem", typeof(OrderItem) },
        { "Customer", typeof(Customer) },
        { "Product", typeof(Product) }
    };
});
```

## ⚡ Výkon

- **Caching** - automatické cachování zkompilovaných výrazů
- **Lazy loading** - výrazy se kompilují pouze při prvním použití
- **Connection pooling** - efektivní správa databázových připojení
- **Memory management** - automatické uvolňování paměti

## 🧪 Testování

ExpressionEngine obsahuje rozsáhlou testovací sadu:

- **Unit testy** - testování jednotlivých komponent
- **Integration testy** - testování celého pipeline
- **JSON testy** - testování s reálnými JSON vstupy
- **Business scenario testy** - testování reálných use cases

```bash
# Spuštění všech Expression testů
dotnet test --filter "Expression"

# Spuštění JSON testů
dotnet test --filter "Json"

# Spuštění business scenario testů
dotnet test --filter "BusinessScenario"
```

## 🐛 Troubleshooting

### Časté problémy

1. **Naming collision** - konflikty mezi `Infrastructure.ExpressionEngine.Expression` a `System.Linq.Expressions.Expression`
2. **Missing DbSet** - entity nejsou registrované v ApplicationDbContext
3. **Type conversion** - nesprávné převody typů v ConstantNode
4. **Null reference** - chybějící validace vstupních dat

### Debugging

```csharp
// Povolení detailního logování
services.AddLogging(builder => 
{
    builder.AddConsole();
    builder.SetMinimumLevel(LogLevel.Debug);
});

// Kontrola cache statistik
var stats = expressionCacheService.GetStatistics();
Console.WriteLine($"Cache hits: {stats.HitCount}, misses: {stats.MissCount}");
```

## 📄 Licence

Tento projekt je licencován pod MIT licencí.
