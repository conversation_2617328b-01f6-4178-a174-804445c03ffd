# Testování ExpressionEngine

Tato dokumentace popisuje strategie a techniky pro testování ExpressionEngine systému.

## 🧪 Typy testů

### 1. Unit testy

Testují jednotlivé komponenty izolovaně.

```csharp
[TestFixture]
public class ConstantNodeTests
{
    [Test]
    public void ConstantNode_Integer_ShouldReturnCorrectValue()
    {
        // Arrange
        var node = new ConstantNode
        {
            DataType = ValueType.Integer,
            Value = "42"
        };

        var builder = new ConstantExpressionBuilder();
        var parameter = Expression.Parameter(typeof(object), "entity");

        // Act
        var expression = builder.Build(node, parameter);
        var compiled = Expression.Lambda<Func<object, object>>(expression, parameter).Compile();
        var result = compiled(new object());

        // Assert
        Assert.AreEqual(42, result);
    }

    [Test]
    public void ConstantNode_Decimal_ShouldReturnCorrectValue()
    {
        // Arrange
        var node = new ConstantNode
        {
            DataType = ValueType.Decimal,
            Value = "15.75"
        };

        // Act & Assert
        var result = ExecuteConstantNode(node);
        Assert.AreEqual(15.75m, result);
    }

    [TestCase("true", true)]
    [TestCase("false", false)]
    public void ConstantNode_Boolean_ShouldReturnCorrectValue(string value, bool expected)
    {
        // Arrange
        var node = new ConstantNode
        {
            DataType = ValueType.Boolean,
            Value = value
        };

        // Act
        var result = ExecuteConstantNode(node);

        // Assert
        Assert.AreEqual(expected, result);
    }
}
```

### 2. Integration testy

Testují celý pipeline od JSON po výsledek.

```csharp
[TestFixture]
public class ExpressionEngineIntegrationTests : IDisposable
{
    private ApplicationDbContext _context;
    private CalculationEngine _calculationEngine;
    private ExpressionRepository _repository;
    private ServiceProvider _serviceProvider;

    [SetUp]
    public void Setup()
    {
        var services = new ServiceCollection();
        
        // Registrace všech potřebných služeb
        services.AddDbContext<ApplicationDbContext>(options =>
            options.UseInMemoryDatabase(Guid.NewGuid().ToString()));
        
        services.AddScoped<CalculationEngine>();
        services.AddScoped<ExpressionRepository>();
        services.AddScoped<IExpressionBuilder, ExpressionBuilder>();
        // ... další registrace

        _serviceProvider = services.BuildServiceProvider();
        _context = _serviceProvider.GetRequiredService<ApplicationDbContext>();
        _calculationEngine = _serviceProvider.GetRequiredService<CalculationEngine>();
        _repository = _serviceProvider.GetRequiredService<ExpressionRepository>();

        _context.Database.EnsureCreated();
    }

    [Test]
    public async Task ComplexExpression_WithLookupAndAggregation_ShouldWork()
    {
        // Arrange
        await SeedTestDataAsync();
        
        var expression = await CreateComplexExpressionAsync();
        var testOrder = CreateTestOrder();

        // Act
        var result = await _calculationEngine.ExecuteAsync(expression, testOrder);

        // Assert
        Assert.IsNotNull(result);
        Assert.AreEqual(150m, Convert.ToDecimal(result));
    }

    private async Task<Expression> CreateComplexExpressionAsync()
    {
        var jsonExpression = """
        {
          "NodeType": "Operation",
          "Operator": "If",
          "Operands": [
            {
              "NodeType": "Operation",
              "Operator": "GreaterThan",
              "Operands": [
                {
                  "NodeType": "Lookup",
                  "TargetEntityName": "Customer",
                  "ReturnFieldPath": "TotalOrders",
                  "Condition": {
                    "NodeType": "Operation",
                    "Operator": "Equal",
                    "Operands": [
                      {
                        "NodeType": "SourceValue",
                        "SourcePath": "Id"
                      },
                      {
                        "NodeType": "SourceValue",
                        "SourcePath": "CustomerId"
                      }
                    ]
                  }
                },
                {
                  "NodeType": "Constant",
                  "DataType": "Integer",
                  "Value": "10"
                }
              ]
            },
            {
              "NodeType": "Operation",
              "Operator": "Multiply",
              "Operands": [
                {
                  "NodeType": "Aggregation",
                  "AggregationType": "Sum",
                  "CollectionPath": "Items",
                  "ValuePath": "LineTotalWithTax"
                },
                {
                  "NodeType": "Constant",
                  "DataType": "Decimal",
                  "Value": "0.1"
                }
              ]
            },
            {
              "NodeType": "Constant",
              "DataType": "Decimal",
              "Value": "0"
            }
          ]
        }
        """;

        var expressionTree = JsonSerializer.Deserialize<ExpressionNode>(
            jsonExpression, 
            DefaultJsonSerializerOptions.Options
        );

        var expression = new Expression
        {
            Id = Guid.NewGuid(),
            Name = "Complex Test Expression",
            Description = "Test složitého výrazu s Lookup a Agregací",
            ExpressionTree = expressionTree!,
            IsActive = true,
            Version = 1,
            EffectiveFrom = DateTime.UtcNow,
            SchemaVersion = "1.0"
        };

        await _repository.AddAsync(expression);
        return expression;
    }
}
```

### 3. JSON testy

Testují deserializaci a vykonávání JSON výrazů.

```csharp
[TestFixture]
public class JsonExpressionTests
{
    [Test]
    public async Task JsonConstant_Integer_ShouldDeserializeAndExecute()
    {
        // Arrange
        var jsonExpression = """
        {
            "NodeType": "Constant",
            "DataType": "Integer",
            "Value": "42"
        }
        """;

        var expression = await CreateExpressionFromJson(
            "JSON Integer Test", 
            "Test JSON deserializace", 
            jsonExpression
        );

        var testEntity = new SampleEntity { Id = 1, Name = "Test" };

        // Act
        var result = await _calculationEngine.ExecuteAsync(expression, testEntity);

        // Assert
        Assert.AreEqual(42, Convert.ToInt32(result));
    }

    [Test]
    public async Task JsonOperation_ComplexIf_ShouldWork()
    {
        // Arrange
        var jsonExpression = """
        {
          "NodeType": "Operation",
          "Operator": "If",
          "Operands": [
            {
              "NodeType": "Operation",
              "Operator": "And",
              "Operands": [
                {
                  "NodeType": "Operation",
                  "Operator": "Equal",
                  "Operands": [
                    {
                      "NodeType": "SourceValue",
                      "SourcePath": "Name"
                    },
                    {
                      "NodeType": "Constant",
                      "DataType": "String",
                      "Value": "VIP"
                    }
                  ]
                },
                {
                  "NodeType": "Operation",
                  "Operator": "GreaterThan",
                  "Operands": [
                    {
                      "NodeType": "SourceValue",
                      "SourcePath": "TotalAmount"
                    },
                    {
                      "NodeType": "Constant",
                      "DataType": "Decimal",
                      "Value": "1000"
                    }
                  ]
                }
              ]
            },
            {
              "NodeType": "Constant",
              "DataType": "String",
              "Value": "Premium"
            },
            {
              "NodeType": "Constant",
              "DataType": "String",
              "Value": "Standard"
            }
          ]
        }
        """;

        var expression = await CreateExpressionFromJson(
            "VIP Check", 
            "Test VIP zákazníka", 
            jsonExpression
        );

        var testEntity = new Order 
        { 
            CustomerName = "VIP",
            TotalAmount = 1500m 
        };

        // Act
        var result = await _calculationEngine.ExecuteAsync(expression, testEntity);

        // Assert
        Assert.AreEqual("Premium", result?.ToString());
    }
}
```

### 4. Business scenario testy

Testují reálné business případy.

```csharp
[TestFixture]
public class BusinessScenarioTests
{
    [Test]
    public async Task VipCustomerDiscount_ShouldCalculateCorrectly()
    {
        // Arrange
        var discountExpression = await CreateVipDiscountExpression();
        
        var vipOrder = new Order
        {
            CustomerType = "VIP",
            TotalAmount = 2000m
        };

        var standardOrder = new Order
        {
            CustomerType = "Standard",
            TotalAmount = 2000m
        };

        // Act
        var vipDiscount = await _calculationEngine.ExecuteAsync<decimal>(
            discountExpression, vipOrder);
        var standardDiscount = await _calculationEngine.ExecuteAsync<decimal>(
            discountExpression, standardOrder);

        // Assert
        Assert.AreEqual(300m, vipDiscount); // 15% z 2000
        Assert.AreEqual(0m, standardDiscount); // Žádná sleva
    }

    [Test]
    public async Task ShippingCost_ShouldBeCorrect()
    {
        // Arrange
        var shippingExpression = await CreateShippingCostExpression();

        var testCases = new[]
        {
            new { Order = new Order { TotalAmount = 500m, CustomerType = "Standard" }, Expected = 150m },
            new { Order = new Order { TotalAmount = 1600m, CustomerType = "Standard" }, Expected = 0m },
            new { Order = new Order { TotalAmount = 100m, CustomerType = "VIP" }, Expected = 0m }
        };

        foreach (var testCase in testCases)
        {
            // Act
            var shippingCost = await _calculationEngine.ExecuteAsync<decimal>(
                shippingExpression, testCase.Order);

            // Assert
            Assert.AreEqual(testCase.Expected, shippingCost,
                $"Shipping cost for {testCase.Order.CustomerType} customer with {testCase.Order.TotalAmount} should be {testCase.Expected}");
        }
    }
}
```

## 🎯 Test utilities

### Test data builders

```csharp
public class OrderBuilder
{
    private Order _order = new Order();

    public OrderBuilder WithCustomerType(string customerType)
    {
        _order.CustomerType = customerType;
        return this;
    }

    public OrderBuilder WithTotalAmount(decimal amount)
    {
        _order.TotalAmount = amount;
        return this;
    }

    public OrderBuilder WithItems(params OrderItem[] items)
    {
        _order.Items = items.ToList();
        return this;
    }

    public Order Build() => _order;

    // Fluent API pro snadné vytváření testovacích dat
    public static OrderBuilder Create() => new OrderBuilder();
}

// Použití
var order = OrderBuilder.Create()
    .WithCustomerType("VIP")
    .WithTotalAmount(1500m)
    .WithItems(
        new OrderItem { ProductCode = "PROD1", Quantity = 2, UnitPrice = 500m },
        new OrderItem { ProductCode = "PROD2", Quantity = 1, UnitPrice = 500m }
    )
    .Build();
```

### Expression builders pro testy

```csharp
public class ExpressionTestBuilder
{
    public static Expression CreateSimpleConstant(string name, ValueType dataType, string value)
    {
        return new Expression
        {
            Id = Guid.NewGuid(),
            Name = name,
            Description = $"Test constant {dataType}",
            ExpressionTree = new ConstantNode
            {
                DataType = dataType,
                Value = value
            },
            IsActive = true,
            Version = 1,
            EffectiveFrom = DateTime.UtcNow,
            SchemaVersion = "1.0"
        };
    }

    public static Expression CreateSimpleOperation(string name, OperatorType operatorType, params ExpressionNode[] operands)
    {
        return new Expression
        {
            Id = Guid.NewGuid(),
            Name = name,
            Description = $"Test operation {operatorType}",
            ExpressionTree = new OperationNode
            {
                Operator = operatorType,
                Operands = operands.ToList()
            },
            IsActive = true,
            Version = 1,
            EffectiveFrom = DateTime.UtcNow,
            SchemaVersion = "1.0"
        };
    }

    public static Expression CreateFromJson(string name, string description, string jsonExpressionTree)
    {
        var expressionTree = JsonSerializer.Deserialize<ExpressionNode>(
            jsonExpressionTree, 
            DefaultJsonSerializerOptions.Options
        );

        return new Expression
        {
            Id = Guid.NewGuid(),
            Name = name,
            Description = description,
            ExpressionTree = expressionTree!,
            IsActive = true,
            Version = 1,
            EffectiveFrom = DateTime.UtcNow,
            SchemaVersion = "1.0"
        };
    }
}
```

### Mock služby

```csharp
public class MockExpressionDataProvider : IExpressionDataProvider
{
    private readonly Dictionary<string, List<object>> _mockData = new();

    public void AddMockData<T>(string entityName, IEnumerable<T> data)
    {
        _mockData[entityName] = data.Cast<object>().ToList();
    }

    public object FindSingle(string entityName, System.Linq.Expressions.Expression filterExpression)
    {
        if (!_mockData.ContainsKey(entityName))
        {
            throw new NotFoundException(entityName, filterExpression.ToString());
        }

        var data = _mockData[entityName];
        var compiled = ((LambdaExpression)filterExpression).Compile();
        
        var result = data.FirstOrDefault(item => (bool)compiled.DynamicInvoke(item)!);
        
        if (result == null)
        {
            throw new NotFoundException(entityName, filterExpression.ToString());
        }

        return result;
    }

    public IEnumerable<object> FindMany(string entityName, System.Linq.Expressions.Expression filterExpression)
    {
        if (!_mockData.ContainsKey(entityName))
        {
            return Enumerable.Empty<object>();
        }

        var data = _mockData[entityName];
        var compiled = ((LambdaExpression)filterExpression).Compile();
        
        return data.Where(item => (bool)compiled.DynamicInvoke(item)!);
    }
}

// Použití v testech
[SetUp]
public void Setup()
{
    var mockDataProvider = new MockExpressionDataProvider();
    
    // Přidej mock data
    mockDataProvider.AddMockData("Customer", new[]
    {
        new Customer { Id = 1, Name = "John Doe", CustomerType = "VIP", TotalOrders = 15 },
        new Customer { Id = 2, Name = "Jane Smith", CustomerType = "Standard", TotalOrders = 5 }
    });

    // Registruj mock službu
    services.AddSingleton<IExpressionDataProvider>(mockDataProvider);
}
```

## 🔍 Test coverage

### Pokrytí kódu

```bash
# Instalace nástroje pro coverage
dotnet tool install --global dotnet-reportgenerator-globaltool

# Spuštění testů s coverage
dotnet test --collect:"XPlat Code Coverage"

# Generování HTML reportu
reportgenerator -reports:"**/coverage.cobertura.xml" -targetdir:"coverage-report" -reporttypes:Html
```

### Coverage cíle

- **Unit testy**: 90%+ pokrytí pro všechny buildery a core logiku
- **Integration testy**: 80%+ pokrytí pro celý pipeline
- **JSON testy**: 100% pokrytí pro všechny typy uzlů
- **Business testy**: 100% pokrytí pro všechny business scénáře

## 🚀 Performance testy

### Load testing

```csharp
[TestFixture]
public class PerformanceTests
{
    [Test]
    public async Task ExpressionExecution_UnderLoad_ShouldMaintainPerformance()
    {
        // Arrange
        var expression = await CreateComplexExpression();
        var entities = GenerateTestEntities(1000);
        var stopwatch = Stopwatch.StartNew();

        // Act
        var tasks = entities.Select(async entity =>
            await _calculationEngine.ExecuteAsync(expression, entity));
        
        var results = await Task.WhenAll(tasks);
        stopwatch.Stop();

        // Assert
        Assert.AreEqual(1000, results.Length);
        Assert.Less(stopwatch.ElapsedMilliseconds, 5000); // Méně než 5 sekund
        
        var averageTime = stopwatch.ElapsedMilliseconds / 1000.0;
        Assert.Less(averageTime, 5); // Průměrně méně než 5ms na výraz
    }

    [Test]
    public void CachePerformance_ShouldImproveWithRepeatedAccess()
    {
        // Arrange
        var expression = CreateSimpleExpression();
        var entity = new SampleEntity();

        // První vykonání (bez cache)
        var firstExecutionTime = MeasureExecutionTime(() =>
            _calculationEngine.Execute(expression, entity));

        // Druhé vykonání (s cache)
        var secondExecutionTime = MeasureExecutionTime(() =>
            _calculationEngine.Execute(expression, entity));

        // Assert
        Assert.Less(secondExecutionTime, firstExecutionTime * 0.1); // 10x rychlejší s cache
    }

    private long MeasureExecutionTime(Action action)
    {
        var stopwatch = Stopwatch.StartNew();
        action();
        stopwatch.Stop();
        return stopwatch.ElapsedMilliseconds;
    }
}
```

### Memory testing

```csharp
[Test]
public void ExpressionCache_ShouldNotLeakMemory()
{
    // Arrange
    var initialMemory = GC.GetTotalMemory(true);
    
    // Act - vytvoř a vykonej mnoho výrazů
    for (int i = 0; i < 1000; i++)
    {
        var expression = CreateUniqueExpression(i);
        _calculationEngine.Execute(expression, new SampleEntity());
    }

    // Force garbage collection
    GC.Collect();
    GC.WaitForPendingFinalizers();
    GC.Collect();

    var finalMemory = GC.GetTotalMemory(false);
    var memoryIncrease = finalMemory - initialMemory;

    // Assert - paměť by neměla růst neomezeně
    Assert.Less(memoryIncrease, 50 * 1024 * 1024); // Méně než 50MB
}
```

## 📊 Continuous Integration

### GitHub Actions workflow

```yaml
name: ExpressionEngine Tests

on:
  push:
    branches: [ main, develop ]
  pull_request:
    branches: [ main ]

jobs:
  test:
    runs-on: ubuntu-latest
    
    steps:
    - uses: actions/checkout@v3
    
    - name: Setup .NET
      uses: actions/setup-dotnet@v3
      with:
        dotnet-version: '8.0.x'
        
    - name: Restore dependencies
      run: dotnet restore
      
    - name: Build
      run: dotnet build --no-restore
      
    - name: Run Unit Tests
      run: dotnet test --no-build --verbosity normal --filter "Category=Unit"
      
    - name: Run Integration Tests
      run: dotnet test --no-build --verbosity normal --filter "Category=Integration"
      
    - name: Run JSON Tests
      run: dotnet test --no-build --verbosity normal --filter "Json"
      
    - name: Run Performance Tests
      run: dotnet test --no-build --verbosity normal --filter "Category=Performance"
      
    - name: Generate Coverage Report
      run: |
        dotnet test --collect:"XPlat Code Coverage"
        dotnet tool install --global dotnet-reportgenerator-globaltool
        reportgenerator -reports:"**/coverage.cobertura.xml" -targetdir:"coverage-report" -reporttypes:Html
        
    - name: Upload Coverage Reports
      uses: actions/upload-artifact@v3
      with:
        name: coverage-report
        path: coverage-report/
```

### Test kategorization

```csharp
[TestFixture]
[Category("Unit")]
public class ConstantNodeTests
{
    // Unit testy
}

[TestFixture]
[Category("Integration")]
public class ExpressionEngineIntegrationTests
{
    // Integration testy
}

[TestFixture]
[Category("Performance")]
public class PerformanceTests
{
    // Performance testy
}

[TestFixture]
[Category("Json")]
public class JsonExpressionTests
{
    // JSON testy
}
```
