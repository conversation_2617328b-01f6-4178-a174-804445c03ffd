# JSON formát a serializace

ExpressionEngine používá JSON formát pro definování výrazů. Tato dokumentace popisuje strukturu JSON, serializaci/deserializaci a best practices.

## 🏗️ Základní struktura

Každý ExpressionNode má povinnou vlastnost `NodeType`, kter<PERSON> určuje typ uzlu:

```json
{
  "NodeType": "Constant|SourceValue|Operation|Lookup|Aggregation",
  // ... dal<PERSON>í vlastnosti specifické pro typ uzlu
}
```

## 📝 JSON Schema

### Expression entita

```json
{
  "Id": "guid",
  "Name": "string",
  "Description": "string", 
  "ExpressionTree": {
    // ExpressionNode JSON
  },
  "IsActive": true,
  "Version": 1,
  "EffectiveFrom": "2024-01-15T10:30:00Z",
  "EffectiveTo": null,
  "SchemaVersion": "1.0",
  "CreatedAt": "2024-01-15T10:30:00Z",
  "CreatedBy": "<EMAIL>",
  "UpdatedAt": "2024-01-15T10:30:00Z", 
  "UpdatedBy": "<EMAIL>"
}
```

### ExpressionNode typy

#### ConstantNode
```json
{
  "NodeType": "Constant",
  "DataType": "Integer|Decimal|String|Boolean|DateTime",
  "Value": "string representation of value"
}
```

#### SourceValueNode
```json
{
  "NodeType": "SourceValue",
  "SourcePath": "PropertyName"
}
```

#### OperationNode
```json
{
  "NodeType": "Operation",
  "Operator": "Add|Subtract|Equal|If|And|Or|...",
  "Operands": [
    // Array of ExpressionNode objects
  ]
}
```

#### LookupNode
```json
{
  "NodeType": "Lookup",
  "TargetEntityName": "EntityName",
  "ReturnFieldPath": "PropertyName",
  "Condition": {
    // ExpressionNode object
  }
}
```

#### AggregationNode
```json
{
  "NodeType": "Aggregation",
  "AggregationType": "Count|Sum|Average|Min|Max",
  "CollectionPath": "PropertyName",
  "ValuePath": "PropertyName", // volitelné pro Sum, Average, Min, Max
  "Condition": {
    // ExpressionNode object - volitelné
  }
}
```

## 🔄 Serializace a deserializace

### Automatická serializace

ExpressionEngine používá vlastní JSON converter `ExpressionNodeJsonConverter`, který automaticky zpracovává polymorfní serializaci ExpressionNode objektů.

```csharp
// Konfigurace JSON serializace
var options = new JsonSerializerOptions
{
    PropertyNamingPolicy = JsonNamingPolicy.CamelCase,
    WriteIndented = true,
    Converters = { new ExpressionNodeJsonConverter() }
};

// Serializace
var json = JsonSerializer.Serialize(expressionNode, options);

// Deserializace
var node = JsonSerializer.Deserialize<ExpressionNode>(json, options);
```

### Ruční vytvoření z JSON

```csharp
// JSON string
var jsonExpression = """
{
  "NodeType": "Operation",
  "Operator": "If",
  "Operands": [
    {
      "NodeType": "SourceValue",
      "SourcePath": "IsActive"
    },
    {
      "NodeType": "Constant",
      "DataType": "String",
      "Value": "Active"
    },
    {
      "NodeType": "Constant", 
      "DataType": "String",
      "Value": "Inactive"
    }
  ]
}
""";

// Deserializace
var expressionTree = JsonSerializer.Deserialize<ExpressionNode>(
    jsonExpression, 
    DefaultJsonSerializerOptions.Options
);

// Vytvoření Expression entity
var expression = new Expression
{
    Id = Guid.NewGuid(),
    Name = "Status Check",
    Description = "Kontrola aktivního stavu",
    ExpressionTree = expressionTree,
    IsActive = true,
    Version = 1,
    EffectiveFrom = DateTime.UtcNow,
    SchemaVersion = "1.0"
};
```

## 📋 Kompletní příklady

### 1. Jednoduchý výpočet slevy

```json
{
  "Id": "123e4567-e89b-12d3-a456-************",
  "Name": "VIP Customer Discount",
  "Description": "10% sleva pro VIP zákazníky s objednávkou nad 1000 Kč",
  "ExpressionTree": {
    "NodeType": "Operation",
    "Operator": "If",
    "Operands": [
      {
        "NodeType": "Operation",
        "Operator": "And",
        "Operands": [
          {
            "NodeType": "Operation",
            "Operator": "Equal",
            "Operands": [
              {
                "NodeType": "SourceValue",
                "SourcePath": "CustomerType"
              },
              {
                "NodeType": "Constant",
                "DataType": "String",
                "Value": "VIP"
              }
            ]
          },
          {
            "NodeType": "Operation",
            "Operator": "GreaterThan",
            "Operands": [
              {
                "NodeType": "SourceValue",
                "SourcePath": "TotalAmount"
              },
              {
                "NodeType": "Constant",
                "DataType": "Decimal",
                "Value": "1000"
              }
            ]
          }
        ]
      },
      {
        "NodeType": "Operation",
        "Operator": "Multiply",
        "Operands": [
          {
            "NodeType": "SourceValue",
            "SourcePath": "TotalAmount"
          },
          {
            "NodeType": "Constant",
            "DataType": "Decimal",
            "Value": "0.1"
          }
        ]
      },
      {
        "NodeType": "Constant",
        "DataType": "Decimal",
        "Value": "0"
      }
    ]
  },
  "IsActive": true,
  "Version": 1,
  "EffectiveFrom": "2024-01-15T00:00:00Z",
  "EffectiveTo": null,
  "SchemaVersion": "1.0"
}
```

### 2. Složitý výraz s Lookup a Agregací

```json
{
  "Id": "456e7890-e89b-12d3-a456-426614174001",
  "Name": "Customer Loyalty Bonus",
  "Description": "Bonus na základě historie objednávek zákazníka",
  "ExpressionTree": {
    "NodeType": "Operation",
    "Operator": "If",
    "Operands": [
      {
        "NodeType": "Operation",
        "Operator": "GreaterThan",
        "Operands": [
          {
            "NodeType": "Lookup",
            "TargetEntityName": "Customer",
            "ReturnFieldPath": "TotalOrdersCount",
            "Condition": {
              "NodeType": "Operation",
              "Operator": "Equal",
              "Operands": [
                {
                  "NodeType": "SourceValue",
                  "SourcePath": "Id"
                },
                {
                  "NodeType": "SourceValue",
                  "SourcePath": "CustomerId"
                }
              ]
            }
          },
          {
            "NodeType": "Constant",
            "DataType": "Integer",
            "Value": "10"
          }
        ]
      },
      {
        "NodeType": "Operation",
        "Operator": "Multiply",
        "Operands": [
          {
            "NodeType": "Aggregation",
            "AggregationType": "Sum",
            "CollectionPath": "Items",
            "ValuePath": "LineTotalWithTax"
          },
          {
            "NodeType": "Constant",
            "DataType": "Decimal",
            "Value": "0.05"
          }
        ]
      },
      {
        "NodeType": "Constant",
        "DataType": "Decimal",
        "Value": "0"
      }
    ]
  },
  "IsActive": true,
  "Version": 1,
  "EffectiveFrom": "2024-01-15T00:00:00Z",
  "SchemaVersion": "1.0"
}
```

## 🎯 Best Practices

### 1. Pojmenování a struktura

```json
{
  // ✅ Dobré: Popisný název a popis
  "Name": "VIP Customer Discount Calculation",
  "Description": "Vypočítá slevu 10% pro VIP zákazníky s objednávkou nad 1000 Kč",
  
  // ✅ Dobré: Jasná struktura s odsazením
  "ExpressionTree": {
    "NodeType": "Operation",
    "Operator": "If",
    "Operands": [
      // Podmínka
      {
        "NodeType": "Operation",
        "Operator": "And",
        "Operands": [...]
      },
      // True hodnota
      {
        "NodeType": "Constant",
        "DataType": "Decimal",
        "Value": "100"
      },
      // False hodnota
      {
        "NodeType": "Constant",
        "DataType": "Decimal", 
        "Value": "0"
      }
    ]
  }
}
```

### 2. Validace JSON

```csharp
// Validace před deserializací
public static bool IsValidExpressionJson(string json)
{
    try
    {
        var node = JsonSerializer.Deserialize<ExpressionNode>(
            json, 
            DefaultJsonSerializerOptions.Options
        );
        return node != null;
    }
    catch (JsonException)
    {
        return false;
    }
}
```

### 3. Verzování

```json
{
  "SchemaVersion": "1.0",
  "Version": 1,
  "EffectiveFrom": "2024-01-15T00:00:00Z",
  "EffectiveTo": "2024-12-31T23:59:59Z"
}
```

### 4. Komentáře v JSON (pro dokumentaci)

```json
{
  "_comment": "Tento výraz počítá slevu pro VIP zákazníky",
  "Name": "VIP Discount",
  "ExpressionTree": {
    "_comment": "IF (CustomerType == 'VIP' AND TotalAmount > 1000) THEN TotalAmount * 0.1 ELSE 0",
    "NodeType": "Operation",
    "Operator": "If",
    "Operands": [...]
  }
}
```

## ⚠️ Časté chyby

### 1. Nesprávný NodeType

```json
// ❌ Špatně: Neexistující NodeType
{
  "NodeType": "InvalidType",
  "Value": "42"
}

// ✅ Správně: Platný NodeType
{
  "NodeType": "Constant",
  "DataType": "Integer",
  "Value": "42"
}
```

### 2. Chybějící povinné vlastnosti

```json
// ❌ Špatně: Chybí DataType u ConstantNode
{
  "NodeType": "Constant",
  "Value": "42"
}

// ✅ Správně: Všechny povinné vlastnosti
{
  "NodeType": "Constant",
  "DataType": "Integer",
  "Value": "42"
}
```

### 3. Nesprávný počet operandů

```json
// ❌ Špatně: IF operátor potřebuje 3 operandy
{
  "NodeType": "Operation",
  "Operator": "If",
  "Operands": [
    {"NodeType": "Constant", "DataType": "Boolean", "Value": "true"},
    {"NodeType": "Constant", "DataType": "String", "Value": "Yes"}
    // Chybí třetí operand pro ELSE větev
  ]
}

// ✅ Správně: 3 operandy pro IF
{
  "NodeType": "Operation", 
  "Operator": "If",
  "Operands": [
    {"NodeType": "Constant", "DataType": "Boolean", "Value": "true"},
    {"NodeType": "Constant", "DataType": "String", "Value": "Yes"},
    {"NodeType": "Constant", "DataType": "String", "Value": "No"}
  ]
}
```

## 🔧 Debugging JSON

### Validace a testování

```csharp
// Test deserializace
var json = """{"NodeType": "Constant", "DataType": "Integer", "Value": "42"}""";

try
{
    var node = JsonSerializer.Deserialize<ExpressionNode>(json, DefaultJsonSerializerOptions.Options);
    Console.WriteLine($"Úspěšně deserializováno: {node.GetType().Name}");
}
catch (JsonException ex)
{
    Console.WriteLine($"Chyba deserializace: {ex.Message}");
}

// Test round-trip serializace
var originalNode = new ConstantNode { DataType = ValueType.Integer, Value = "42" };
var serialized = JsonSerializer.Serialize(originalNode, DefaultJsonSerializerOptions.Options);
var deserialized = JsonSerializer.Deserialize<ExpressionNode>(serialized, DefaultJsonSerializerOptions.Options);

Console.WriteLine($"Round-trip test: {originalNode.GetType() == deserialized.GetType()}");
```
