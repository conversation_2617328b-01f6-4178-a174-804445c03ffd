# API Reference

Tato dokumentace popisuje všechny třídy, rozhraní a metody ExpressionEngine systému.

## 🏗️ Základní komponenty

### CalculationEngine

Hlavní třída pro vykonávání výrazů.

```csharp
public class CalculationEngine
{
    public async Task<object> ExecuteAsync(Expression expression, object entity)
    public async Task<T> ExecuteAsync<T>(Expression expression, object entity)
    public object Execute(Expression expression, object entity)
    public T Execute<T>(Expression expression, object entity)
}
```

#### Metody

##### ExecuteAsync(Expression, object)
Asynchronně vykoná výraz nad zadanou entitou.

**Parametry:**
- `expression` - Expression entita obsahující ExpressionTree
- `entity` - Entita, nad kterou se výraz vykonává

**Návratová hodnota:** `Task<object>` - Výsledek výrazu

**Příklad:**
```csharp
var result = await calculationEngine.ExecuteAsync(expression, order);
var discount = Convert.ToDecimal(result);
```

##### ExecuteAsync<T>(Expression, object)
Asynchronně vykoná výraz s typovaným výsledkem.

**Parametry:**
- `expression` - Expression entita
- `entity` - Entita pro vykonání

**Návratová hodnota:** `Task<T>` - Typovaný výsledek

**Příklad:**
```csharp
var discount = await calculationEngine.ExecuteAsync<decimal>(expression, order);
var isEligible = await calculationEngine.ExecuteAsync<bool>(expression, customer);
```

### ExpressionRepository

Repository pro správu Expression entit.

```csharp
public class ExpressionRepository
{
    public async Task<Expression?> GetByIdAsync(Guid id)
    public async Task<Expression?> GetByNameAsync(string name)
    public async Task<IEnumerable<Expression>> GetActiveExpressionsAsync()
    public async Task<IEnumerable<Expression>> GetAllAsync()
    public async Task AddAsync(Expression expression)
    public async Task UpdateAsync(Expression expression)
    public async Task DeleteAsync(Guid id)
    public async Task<bool> ExistsAsync(Guid id)
}
```

#### Metody

##### GetByIdAsync(Guid)
Najde výraz podle ID.

**Parametry:**
- `id` - Jedinečný identifikátor výrazu

**Návratová hodnota:** `Task<Expression?>` - Nalezený výraz nebo null

##### GetByNameAsync(string)
Najde výraz podle názvu.

**Parametry:**
- `name` - Název výrazu

**Návratová hodnota:** `Task<Expression?>` - Nalezený výraz nebo null

##### GetActiveExpressionsAsync()
Vrátí všechny aktivní výrazy.

**Návratová hodnota:** `Task<IEnumerable<Expression>>` - Kolekce aktivních výrazů

**Příklad:**
```csharp
var activeExpressions = await repository.GetActiveExpressionsAsync();
foreach (var expr in activeExpressions)
{
    if (expr.EffectiveFrom <= DateTime.UtcNow && 
        (expr.EffectiveTo == null || expr.EffectiveTo >= DateTime.UtcNow))
    {
        // Výraz je platný
    }
}
```

## 🧩 ExpressionNode typy

### ExpressionNode (abstract)

Základní abstraktní třída pro všechny uzly výrazu.

```csharp
public abstract class ExpressionNode
{
    public abstract string NodeType { get; }
}
```

### ConstantNode

Reprezentuje konstantní hodnotu.

```csharp
public class ConstantNode : ExpressionNode
{
    public override string NodeType => "Constant";
    public ValueType DataType { get; set; }
    public string Value { get; set; }
}
```

**Vlastnosti:**
- `DataType` - Typ hodnoty (Integer, Decimal, String, Boolean, DateTime)
- `Value` - Textová reprezentace hodnoty

**Příklad:**
```csharp
var constantNode = new ConstantNode
{
    DataType = ValueType.Decimal,
    Value = "15.75"
};
```

### SourceValueNode

Přístup k vlastnostem entity.

```csharp
public class SourceValueNode : ExpressionNode
{
    public override string NodeType => "SourceValue";
    public string SourcePath { get; set; }
}
```

**Vlastnosti:**
- `SourcePath` - Cesta k vlastnosti (např. "Name", "Customer.Email")

### OperationNode

Logické a matematické operace.

```csharp
public class OperationNode : ExpressionNode
{
    public override string NodeType => "Operation";
    public OperatorType Operator { get; set; }
    public List<ExpressionNode> Operands { get; set; } = new();
}
```

**Vlastnosti:**
- `Operator` - Typ operátoru
- `Operands` - Seznam operandů

### LookupNode

Vyhledání v jiné entitě.

```csharp
public class LookupNode : ExpressionNode
{
    public override string NodeType => "Lookup";
    public string TargetEntityName { get; set; }
    public string ReturnFieldPath { get; set; }
    public ExpressionNode Condition { get; set; }
}
```

**Vlastnosti:**
- `TargetEntityName` - Název cílové entity
- `ReturnFieldPath` - Vlastnost k vrácení
- `Condition` - Podmínka pro vyhledání

### AggregationNode

Agregační funkce nad kolekcemi.

```csharp
public class AggregationNode : ExpressionNode
{
    public override string NodeType => "Aggregation";
    public AggregationType AggregationType { get; set; }
    public string CollectionPath { get; set; }
    public string? ValuePath { get; set; }
    public ExpressionNode? Condition { get; set; }
}
```

**Vlastnosti:**
- `AggregationType` - Typ agregace (Count, Sum, Average, Min, Max)
- `CollectionPath` - Cesta ke kolekci
- `ValuePath` - Cesta k hodnotě (pro Sum, Average, Min, Max)
- `Condition` - Volitelná podmínka filtru

## 🔧 Builder rozhraní

### IExpressionBuilder

Hlavní builder pro kompilaci výrazů.

```csharp
public interface IExpressionBuilder
{
    System.Linq.Expressions.Expression<Func<object, object>> Build(ExpressionNode node);
    System.Linq.Expressions.Expression BuildTyped(ExpressionNode node, Type entityType);
}
```

### Specializované buildery

#### IConstantExpressionBuilder
```csharp
public interface IConstantExpressionBuilder
{
    System.Linq.Expressions.Expression Build(ConstantNode node, ParameterExpression parameter);
}
```

#### ISourceExpressionBuilder
```csharp
public interface ISourceExpressionBuilder
{
    System.Linq.Expressions.Expression Build(SourceValueNode node, ParameterExpression parameter);
}
```

#### IOperationExpressionBuilder
```csharp
public interface IOperationExpressionBuilder
{
    System.Linq.Expressions.Expression Build(
        OperationNode node, 
        ParameterExpression parameter,
        Func<ExpressionNode, ParameterExpression, System.Linq.Expressions.Expression> buildChild
    );
}
```

#### ILookupExpressionBuilder
```csharp
public interface ILookupExpressionBuilder
{
    System.Linq.Expressions.Expression Build(
        LookupNode node,
        ParameterExpression parameter,
        Func<ExpressionNode, ParameterExpression, System.Linq.Expressions.Expression> buildChild
    );
}
```

#### IAggregationExpressionBuilder
```csharp
public interface IAggregationExpressionBuilder
{
    System.Linq.Expressions.Expression Build(
        AggregationNode node,
        ParameterExpression parameter
    );
}
```

## 💾 Data Access

### IExpressionDataProvider

Rozhraní pro přístup k datům při Lookup operacích.

```csharp
public interface IExpressionDataProvider
{
    object FindSingle(string entityName, System.Linq.Expressions.Expression filterExpression);
    IEnumerable<object> FindMany(string entityName, System.Linq.Expressions.Expression filterExpression);
}
```

#### Metody

##### FindSingle(string, Expression)
Najde jediný záznam podle filtru.

**Parametry:**
- `entityName` - Název entity
- `filterExpression` - Filtr jako Expression Tree

**Návratová hodnota:** `object` - Nalezený záznam

**Výjimky:**
- `NotFoundException` - Záznam nebyl nalezen
- `InvalidOperationException` - Nalezeno více záznamů

##### FindMany(string, Expression)
Najde více záznamů podle filtru.

**Parametry:**
- `entityName` - Název entity
- `filterExpression` - Filtr jako Expression Tree

**Návratová hodnota:** `IEnumerable<object>` - Kolekce nalezených záznamů

## 🚀 Cache služby

### IExpressionCacheService

Rozhraní pro cachování zkompilovaných výrazů.

```csharp
public interface IExpressionCacheService
{
    void Set<T>(string key, T value, TimeSpan expiration);
    T? Get<T>(string key);
    void Remove(string key);
    void Clear();
    ExpressionCacheStatistics GetStatistics();
}
```

### ExpressionCacheStatistics

Statistiky cache výkonu.

```csharp
public class ExpressionCacheStatistics
{
    public int HitCount { get; set; }
    public int MissCount { get; set; }
    public int TotalRequests => HitCount + MissCount;
    public double HitRatio => TotalRequests > 0 ? (double)HitCount / TotalRequests : 0;
    public int CachedItemsCount { get; set; }
}
```

## 📊 Enumerace

### ValueType

Podporované datové typy pro konstanty.

```csharp
public enum ValueType
{
    Integer,
    Decimal,
    String,
    Boolean,
    DateTime
}
```

### OperatorType

Podporované operátory.

```csharp
public enum OperatorType
{
    // Matematické
    Add,
    Subtract,
    Multiply,
    Divide,
    
    // Porovnávací
    Equal,
    NotEqual,
    GreaterThan,
    GreaterThanOrEqual,
    LessThan,
    LessThanOrEqual,
    
    // Logické
    And,
    Or,
    Not,
    
    // Podmíněné
    If,
    
    // Textové
    Contains,
    StartsWith,
    EndsWith
}
```

### AggregationType

Podporované agregační funkce.

```csharp
public enum AggregationType
{
    Count,
    Sum,
    Average,
    Min,
    Max
}
```

## 🔧 Konfigurace

### Dependency Injection registrace

```csharp
// V Program.cs nebo Startup.cs
public static void AddExpressionEngine(this IServiceCollection services)
{
    // Hlavní služby
    services.AddScoped<CalculationEngine>();
    services.AddScoped<ExpressionRepository>();
    services.AddScoped<IExpressionBuilder, ExpressionBuilder>();
    services.AddScoped<IExpressionDataProvider, ExpressionDataProvider>();

    // Buildery
    services.AddScoped<IConstantExpressionBuilder, ConstantExpressionBuilder>();
    services.AddScoped<ISourceExpressionBuilder, SourceExpressionBuilder>();
    services.AddScoped<IOperationExpressionBuilder, OperationExpressionBuilder>();
    services.AddScoped<ILookupExpressionBuilder, LookupExpressionBuilder>();
    services.AddScoped<IAggregationExpressionBuilder, AggregationExpressionBuilder>();

    // Cache
    services.AddSingleton<IExpressionCacheService, ExpressionCacheService>();
    
    // Entity type mapping
    services.AddSingleton<IReadOnlyDictionary<string, Type>>(provider =>
    {
        return new Dictionary<string, Type>
        {
            { "Order", typeof(Order) },
            { "Customer", typeof(Customer) },
            { "Product", typeof(Product) }
        };
    });
}
```

## ⚠️ Výjimky

### ExpressionExecutionException

Výjimka při vykonávání výrazu.

```csharp
public class ExpressionExecutionException : Exception
{
    public ExpressionNode? Node { get; }
    public object? Entity { get; }
    
    public ExpressionExecutionException(string message, ExpressionNode? node = null, object? entity = null)
    public ExpressionExecutionException(string message, Exception innerException, ExpressionNode? node = null, object? entity = null)
}
```

### NotFoundException

Výjimka při nenalezení entity v Lookup operaci.

```csharp
public class NotFoundException : Exception
{
    public string EntityName { get; }
    public string Condition { get; }
    
    public NotFoundException(string entityName, string condition)
}
```

## 📝 Příklady použití

### Základní použití

```csharp
// Vytvoření a uložení výrazu
var expression = new Expression
{
    Name = "Discount Calculation",
    ExpressionTree = new OperationNode
    {
        Operator = OperatorType.Multiply,
        Operands = new List<ExpressionNode>
        {
            new SourceValueNode { SourcePath = "TotalAmount" },
            new ConstantNode { DataType = ValueType.Decimal, Value = "0.1" }
        }
    }
};

await repository.AddAsync(expression);

// Vykonání výrazu
var order = new Order { TotalAmount = 1000m };
var discount = await calculationEngine.ExecuteAsync<decimal>(expression, order);
// Výsledek: 100m
```

### Složitý výraz s Lookup

```csharp
var expression = new Expression
{
    Name = "Customer Loyalty Check",
    ExpressionTree = new LookupNode
    {
        TargetEntityName = "Customer",
        ReturnFieldPath = "LoyaltyLevel",
        Condition = new OperationNode
        {
            Operator = OperatorType.Equal,
            Operands = new List<ExpressionNode>
            {
                new SourceValueNode { SourcePath = "Id" },
                new SourceValueNode { SourcePath = "CustomerId" }
            }
        }
    }
};

var loyaltyLevel = await calculationEngine.ExecuteAsync<string>(expression, order);
```
