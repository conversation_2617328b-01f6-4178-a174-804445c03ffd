# Typy uzlů (ExpressionNode)

ExpressionEngine podporuje pět základních typů uzlů, kter<PERSON> lze kombinovat pro vytváření složitých výrazů.

## 1. ConstantNode - Konstantní hodnoty

Reprezentuje konstantní hodnotu v výrazu.

### Podporované datové typy

| DataType | Popis | Příklad hodnoty |
|----------|-------|-----------------|
| `Integer` | Celé číslo | `"42"`, `"-10"`, `"0"` |
| `Decimal` | Desetinné číslo | `"15.75"`, `"-3.14"`, `"0.0"` |
| `String` | Textový řetězec | `"Hello World"`, `""`, `"VIP"` |
| `Boolean` | Logická hodnota | `"true"`, `"false"` |
| `DateTime` | Datum a čas | `"2024-01-15T10:30:00"` |

### JSON formát

```json
{
  "NodeType": "Constant",
  "DataType": "Integer",
  "Value": "42"
}
```

### Příklady použití

```json
// Celé číslo
{
  "NodeType": "Constant",
  "DataType": "Integer", 
  "Value": "100"
}

// Desetinné číslo
{
  "NodeType": "Constant",
  "DataType": "Decimal",
  "Value": "15.75"
}

// Text
{
  "NodeType": "Constant",
  "DataType": "String",
  "Value": "Premium Customer"
}

// Boolean
{
  "NodeType": "Constant",
  "DataType": "Boolean",
  "Value": "true"
}

// Datum
{
  "NodeType": "Constant",
  "DataType": "DateTime",
  "Value": "2024-01-15T10:30:00"
}
```

## 2. SourceValueNode - Přístup k vlastnostem

Umožňuje přístup k vlastnostem aktuální entity.

### JSON formát

```json
{
  "NodeType": "SourceValue",
  "SourcePath": "PropertyName"
}
```

### Podporované cesty

- **Jednoduché vlastnosti**: `"Name"`, `"TotalAmount"`, `"IsActive"`
- **Vnořené vlastnosti**: `"Customer.Name"`, `"Address.City"`
- **Kolekce**: `"Items.Count"` (pouze pro počet prvků)

### Příklady použití

```json
// Přístup k názvu
{
  "NodeType": "SourceValue",
  "SourcePath": "Name"
}

// Přístup k celkové částce
{
  "NodeType": "SourceValue", 
  "SourcePath": "TotalAmount"
}

// Přístup k boolean vlastnosti
{
  "NodeType": "SourceValue",
  "SourcePath": "IsActive"
}

// Vnořená vlastnost
{
  "NodeType": "SourceValue",
  "SourcePath": "Customer.Email"
}
```

## 3. OperationNode - Operace a funkce

Provádí logické, matematické a podmíněné operace.

### JSON formát

```json
{
  "NodeType": "Operation",
  "Operator": "OperatorName",
  "Operands": [...]
}
```

### Podporované operátory

#### Matematické operátory
- `Add` - sčítání (2 operandy)
- `Subtract` - odčítání (2 operandy)
- `Multiply` - násobení (2 operandy)
- `Divide` - dělení (2 operandy)

#### Porovnávací operátory
- `Equal` - rovnost (2 operandy)
- `NotEqual` - nerovnost (2 operandy)
- `GreaterThan` - větší než (2 operandy)
- `GreaterThanOrEqual` - větší nebo rovno (2 operandy)
- `LessThan` - menší než (2 operandy)
- `LessThanOrEqual` - menší nebo rovno (2 operandy)

#### Logické operátory
- `And` - logické AND (2 operandy)
- `Or` - logické OR (2 operandy)
- `Not` - logické NOT (1 operand)

#### Podmíněné operátory
- `If` - podmíněný výraz (3 operandy: podmínka, true hodnota, false hodnota)

#### Textové operátory
- `Contains` - obsahuje text (2 operandy)
- `StartsWith` - začína textem (2 operandy)
- `EndsWith` - končí textem (2 operandy)

### Příklady použití

```json
// Sčítání
{
  "NodeType": "Operation",
  "Operator": "Add",
  "Operands": [
    {
      "NodeType": "SourceValue",
      "SourcePath": "BasePrice"
    },
    {
      "NodeType": "SourceValue", 
      "SourcePath": "Tax"
    }
  ]
}

// Porovnání
{
  "NodeType": "Operation",
  "Operator": "GreaterThan",
  "Operands": [
    {
      "NodeType": "SourceValue",
      "SourcePath": "TotalAmount"
    },
    {
      "NodeType": "Constant",
      "DataType": "Decimal",
      "Value": "1000"
    }
  ]
}

// Podmíněný výraz (IF-THEN-ELSE)
{
  "NodeType": "Operation",
  "Operator": "If",
  "Operands": [
    // Podmínka
    {
      "NodeType": "Operation",
      "Operator": "Equal",
      "Operands": [
        {
          "NodeType": "SourceValue",
          "SourcePath": "CustomerType"
        },
        {
          "NodeType": "Constant",
          "DataType": "String",
          "Value": "VIP"
        }
      ]
    },
    // True hodnota
    {
      "NodeType": "Constant",
      "DataType": "String",
      "Value": "Premium Service"
    },
    // False hodnota
    {
      "NodeType": "Constant",
      "DataType": "String", 
      "Value": "Standard Service"
    }
  ]
}

// Logické AND
{
  "NodeType": "Operation",
  "Operator": "And",
  "Operands": [
    {
      "NodeType": "SourceValue",
      "SourcePath": "IsActive"
    },
    {
      "NodeType": "Operation",
      "Operator": "GreaterThan",
      "Operands": [
        {
          "NodeType": "SourceValue",
          "SourcePath": "TotalAmount"
        },
        {
          "NodeType": "Constant",
          "DataType": "Decimal",
          "Value": "100"
        }
      ]
    }
  ]
}
```

## 4. LookupNode - Vyhledání v jiné entitě

Umožňuje vyhledání hodnoty v jiné entitě na základě podmínky.

### JSON formát

```json
{
  "NodeType": "Lookup",
  "TargetEntityName": "EntityName",
  "ReturnFieldPath": "PropertyName",
  "Condition": {...}
}
```

### Parametry

- `TargetEntityName` - název cílové entity (musí být registrována v EntityTypeMap)
- `ReturnFieldPath` - cesta k vlastnosti, kterou chceme vrátit
- `Condition` - podmínka pro vyhledání (ExpressionNode)

### Příklady použití

```json
// Najdi objednávku podle čísla a vrať její celkovou částku
{
  "NodeType": "Lookup",
  "TargetEntityName": "Order",
  "ReturnFieldPath": "TotalAmount",
  "Condition": {
    "NodeType": "Operation",
    "Operator": "Equal",
    "Operands": [
      {
        "NodeType": "SourceValue",
        "SourcePath": "OrderNumber"
      },
      {
        "NodeType": "Constant",
        "DataType": "String",
        "Value": "ORD-12345"
      }
    ]
  }
}

// Najdi zákazníka podle emailu a vrať jeho typ
{
  "NodeType": "Lookup",
  "TargetEntityName": "Customer",
  "ReturnFieldPath": "CustomerType",
  "Condition": {
    "NodeType": "Operation",
    "Operator": "Equal",
    "Operands": [
      {
        "NodeType": "SourceValue",
        "SourcePath": "Email"
      },
      {
        "NodeType": "SourceValue",
        "SourcePath": "CustomerEmail"
      }
    ]
  }
}
```

## 5. AggregationNode - Agregační funkce

Provádí agregační operace nad kolekcemi.

### JSON formát

```json
{
  "NodeType": "Aggregation",
  "AggregationType": "Count|Sum|Average|Min|Max",
  "CollectionPath": "PropertyName",
  "Condition": {...} // volitelné
}
```

### Podporované agregace

- `Count` - počet prvků
- `Sum` - suma hodnot
- `Average` - průměr hodnot
- `Min` - minimální hodnota
- `Max` - maximální hodnota

### Příklady použití

```json
// Počet položek v objednávce
{
  "NodeType": "Aggregation",
  "AggregationType": "Count",
  "CollectionPath": "Items"
}

// Suma všech položek
{
  "NodeType": "Aggregation",
  "AggregationType": "Sum",
  "CollectionPath": "Items",
  "ValuePath": "LineTotalWithTax"
}

// Průměrná cena položek
{
  "NodeType": "Aggregation",
  "AggregationType": "Average",
  "CollectionPath": "Items",
  "ValuePath": "UnitPrice"
}

// Počet aktivních položek (s podmínkou)
{
  "NodeType": "Aggregation",
  "AggregationType": "Count",
  "CollectionPath": "Items",
  "Condition": {
    "NodeType": "Operation",
    "Operator": "Equal",
    "Operands": [
      {
        "NodeType": "SourceValue",
        "SourcePath": "IsActive"
      },
      {
        "NodeType": "Constant",
        "DataType": "Boolean",
        "Value": "true"
      }
    ]
  }
}
```

## Kombinování uzlů

Uzly lze libovolně kombinovat pro vytváření složitých výrazů:

```json
{
  "NodeType": "Operation",
  "Operator": "If",
  "Operands": [
    // Podmínka: Lookup + porovnání
    {
      "NodeType": "Operation",
      "Operator": "GreaterThan",
      "Operands": [
        {
          "NodeType": "Lookup",
          "TargetEntityName": "Customer",
          "ReturnFieldPath": "TotalOrders",
          "Condition": {
            "NodeType": "Operation",
            "Operator": "Equal",
            "Operands": [
              {
                "NodeType": "SourceValue",
                "SourcePath": "Id"
              },
              {
                "NodeType": "SourceValue",
                "SourcePath": "CustomerId"
              }
            ]
          }
        },
        {
          "NodeType": "Constant",
          "DataType": "Integer",
          "Value": "10"
        }
      ]
    },
    // True: Agregace + matematika
    {
      "NodeType": "Operation",
      "Operator": "Multiply",
      "Operands": [
        {
          "NodeType": "Aggregation",
          "AggregationType": "Sum",
          "CollectionPath": "Items",
          "ValuePath": "LineTotalWithTax"
        },
        {
          "NodeType": "Constant",
          "DataType": "Decimal",
          "Value": "0.15"
        }
      ]
    },
    // False: Konstanta
    {
      "NodeType": "Constant",
      "DataType": "Decimal",
      "Value": "0"
    }
  ]
}
```
