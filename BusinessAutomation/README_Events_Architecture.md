# BusinessAutomation - Architektura Událostí

## Přehled

BusinessAutomation projekt implementuje **duální systém událostí**, který kombinuje dva odlišné, ale komplementární přístupy k řízení událostí v aplikaci. Tato dualita nen<PERSON> ch<PERSON>, ale záměrné architektonické rozhodnutí, které umožňuje pokrýt různé potřeby aplikace.

## Dva Typy Událostí

### 1. DDD Doménové Události (Domain Events)
**Silně typované, explicitní, neměnné**

```csharp
// Příklad definice
public class OrderPaidEvent : IDomainEvent
{
    public Guid OrderId { get; }
    public decimal Amount { get; }
    public DateTime PaidAt { get; }
    
    public OrderPaidEvent(Guid orderId, decimal amount, DateTime paidAt)
    {
        OrderId = orderId;
        Amount = amount;
        PaidAt = paidAt;
    }
}

// Příklad použití v AggregateRoot
public class Order : AggregateRoot
{
    public void MarkAsPaid(decimal amount)
    {
        // Byznys logika
        Status = OrderStatus.Paid;
        PaidAt = DateTime.UtcNow;
        
        // Vyvolání doménové události
        AddDomainEvent(new OrderPaidEvent(Id, amount, PaidAt.Value));
    }
}
```

**Charakteristiky:**
- ✅ **Silně typované** - kompilační kontrola
- ✅ **Explicitní** - vývojář explicitně rozhoduje kdy a kde se vyvolají
- ✅ **Neměnné** - součást kódu, mění se pouze s novými verzemi
- ✅ **Byznys logika** - reprezentují klíčové doménové procesy
- ✅ **Vývojářské** - určené pro vývojáře, součást implementace

### 2. Dynamické Datové Události (Dynamic Data Events)
**Slabě typované, automatické, konfigurovatelné**

```csharp
// Příklad definice v databázi
var eventDefinition = new EventDefinition
{
    EventName = "OrderStatusChanged",
    EntityType = typeof(Order),
    Operation = EventType.Update,
    TrackedProperties = new[] { "Status" },
    ConditionExpressionName = "OrderStatusToProcessing",
    Description = "Spustí se při změně stavu objednávky na Processing"
};

// Automatické generování události interceptorem
public class EntityChangedEvent
{
    public string EventName { get; set; }
    public string EntityName { get; set; }
    public object EntityId { get; set; }
    public Dictionary<string, object> ChangedProperties { get; set; }
    public EventType Operation { get; set; }
}
```

**Charakteristiky:**
- ✅ **Slabě typované** - flexibilní, generické
- ✅ **Automatické** - generované interceptorem na základě databázových změn
- ✅ **Konfigurovatelné** - administrátoři mohou měnit bez kódu
- ✅ **Datově orientované** - reagují na změny v databázi
- ✅ **Uživatelské** - určené pro administrátory a power-usery

## Kdy Použít Který Přístup

### DDD Doménové Události - Použijte Pro:

#### ✅ Klíčové Byznys Procesy
```csharp
// Platba objednávky - kritický byznys proces
order.MarkAsPaid(amount);  // → OrderPaidEvent

// Zrušení objednávky - důležitá byznys operace  
order.Cancel(reason);      // → OrderCancelledEvent

// Dokončení dodávky - klíčový milestone
delivery.Complete();       // → DeliveryCompletedEvent
```

#### ✅ Neměnné Procesy
- Procesy, které se nemění s časem
- Základní byznys pravidla aplikace
- Procesy vyžadující silnou typovou kontrolu

#### ✅ Komplexní Byznys Logika
```csharp
public void ProcessPayment(PaymentMethod method, decimal amount)
{
    // Složitá byznys logika
    ValidatePaymentMethod(method);
    ApplyDiscounts();
    CalculateTaxes();
    
    // Explicitní vyvolání události s kontextem
    AddDomainEvent(new PaymentProcessedEvent(Id, method, amount, AppliedDiscounts));
}
```

### Dynamické Datové Události - Použijte Pro:

#### ✅ Notifikace a Alerting
```json
{
  "EventName": "LowStockAlert",
  "EntityType": "Product",
  "Operation": "Update",
  "TrackedProperties": ["StockQuantity"],
  "ConditionExpression": "StockQuantity < MinimumStock"
}
```

#### ✅ Integrace s Externími Systémy
```json
{
  "EventName": "CustomerDataSync",
  "EntityType": "Customer", 
  "Operation": "Update",
  "TrackedProperties": ["Email", "Phone", "Address"],
  "Description": "Synchronizace zákaznických dat s CRM"
}
```

#### ✅ Webhooks a API Callbacks
```json
{
  "EventName": "OrderStatusWebhook",
  "EntityType": "Order",
  "Operation": "Update", 
  "TrackedProperties": ["Status"],
  "Description": "Webhook pro e-shop při změně stavu objednávky"
}
```

#### ✅ Konfigurovatelná Automatizace
```json
{
  "EventName": "AutoApproveSmallOrders",
  "EntityType": "Order",
  "Operation": "Create",
  "ConditionExpression": "TotalAmount < 1000 AND CustomerType == 'Premium'",
  "Description": "Automatické schválení malých objednávek VIP zákazníků"
}
```

## Architektonické Výhody Duálního Systému

### 🎯 Separace Odpovědností
- **DDD události** = Vývojářská odpovědnost, součást kódu
- **Dynamické události** = Uživatelská konfigurace, runtime změny

### 🔧 Flexibilita
- **DDD události** = Stabilní, předvídatelné chování
- **Dynamické události** = Adaptabilní na měnící se požadavky

### 🚀 Škálovatelnost
- **DDD události** = Optimalizované pro výkon, kompilované
- **Dynamické události** = Škálovatelné pro velké množství konfigurací

### 👥 Různé Cílové Skupiny
- **DDD události** = Vývojáři, architekti
- **Dynamické události** = Administrátoři, power-usery, zákazníci

## Implementační Detaily

### DDD Události - Tok
1. Byznys operace v AggregateRoot
2. `AddDomainEvent(event)` 
3. `DomainEventPublisher` při `SaveChanges`
4. Silně typované handlery (`INotificationHandler<OrderPaidEvent>`)

### Dynamické Události - Tok  
1. Databázová změna
2. `TrackableEntityInterceptor` detekuje změnu
3. Kontrola `EventDefinition` v databázi
4. Vyhodnocení podmínek (Expression Engine)
5. Generování `EntityChangedEvent`
6. Generické handlery nebo webhook callbacky

## Doporučené Praktiky

### ✅ DO - Správné Použití

```csharp
// DDD - Klíčový byznys proces
public void CompleteOrder()
{
    Status = OrderStatus.Completed;
    CompletedAt = DateTime.UtcNow;
    AddDomainEvent(new OrderCompletedEvent(Id, CompletedAt.Value));
}

// Dynamické - Konfigurovatelná notifikace
// Definováno v databázi, ne v kódu
```

### ❌ DON'T - Nesprávné Použití

```csharp
// ŠPATNĚ - Používání DDD události pro konfiguraci
public void UpdateEmail(string email)
{
    Email = email;
    // ŠPATNĚ - toto by měla být dynamická událost
    AddDomainEvent(new EmailChangedEvent(Id, email)); 
}

// ŠPATNĚ - Spoléhání na dynamické události pro kritické procesy
// Kritické procesy by měly být DDD události
```

## Monitoring a Debugging

### DDD Události
- Logování v `DomainEventPublisher`
- Unit testy pro handlery
- Silná typová kontrola

### Dynamické Události  
- Logování v `TrackableEntityInterceptor`
- Admin UI pro správu `EventDefinition`
- Expression Engine debugging
- Webhook delivery tracking

## Budoucí Rozšíření

### Možné Vylepšení
1. **Event Sourcing** pro DDD události
2. **Saga Pattern** pro komplexní workflows
3. **Event Store** pro audit trail
4. **Dead Letter Queue** pro failed events
5. **Event Replay** pro debugging

---

**Klíčové Poselství**: Oba přístupy mají své místo! Nejedná se o konkurenční řešení, ale o komplementární nástroje pro různé potřeby aplikace.
