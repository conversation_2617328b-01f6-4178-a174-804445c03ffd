using BusinessAutomation.Core.Abstractions.Database;
using BusinessAutomation.Core.Abstractions.ExpressionEngine.Services;
using BusinessAutomation.Core.Abstractions.Events.Services;
using BusinessAutomation.Core.Infrastructure.Database.Persistence;
using BusinessAutomation.Core.Infrastructure.ExpressionEngine.Services;
using BusinessAutomation.Core.Infrastructure.Events.Services;
using BusinessAutomation.Core.Infrastructure.Common.Services;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;

namespace BusinessAutomation.Core.Infrastructure.DependencyInjection;

/// <summary>
/// Extension metody pro registraci BusinessAutomation služeb do DI kontejneru.
/// </summary>
public static class BusinessAutomationServiceCollectionExtensions
{
    /// <summary>
    /// Přidá BusinessAutomation služby do DI kontejneru.
    /// </summary>
    /// <param name="services">Service collection</param>
    /// <param name="configuration">Konfigurace aplikace</param>
    /// <returns>Service collection pro fluent API</returns>
    public static IServiceCollection AddBusinessAutomation(
        this IServiceCollection services, 
        IConfiguration configuration)
    {
        // Registrace databázového kontextu
        services.AddBusinessAutomationDatabase(configuration);

        // Registrace ExpressionEngine služeb
        services.AddExpressionEngine();

        // Registrace Events služeb
        services.AddEvents();

        // Registrace společných služeb
        services.AddCommonServices();

        return services;
    }

    /// <summary>
    /// Přidá databázovou podporu pro BusinessAutomation.
    /// </summary>
    /// <param name="services">Service collection</param>
    /// <param name="configuration">Konfigurace aplikace</param>
    /// <returns>Service collection pro fluent API</returns>
    public static IServiceCollection AddBusinessAutomationDatabase(
        this IServiceCollection services,
        IConfiguration configuration)
    {
        var connectionString = configuration.GetConnectionString("BusinessAutomationDatabase")
                              ?? configuration.GetConnectionString("DefaultConnection")
                              ?? "Data Source=./Data/businessautomation.db";

        services.AddDbContext<BusinessAutomationDbContext>(options =>
        {
            options.UseSqlite(connectionString);
            options.EnableSensitiveDataLogging(false);
            options.EnableDetailedErrors(false);
        });

        services.AddScoped<IBusinessAutomationDbContext>(provider =>
            provider.GetRequiredService<BusinessAutomationDbContext>());

        return services;
    }

    /// <summary>
    /// Přidá ExpressionEngine služby.
    /// </summary>
    /// <param name="services">Service collection</param>
    /// <returns>Service collection pro fluent API</returns>
    public static IServiceCollection AddExpressionEngine(this IServiceCollection services)
    {
        services.AddScoped<IExpressionRepository, ExpressionRepository>();
        services.AddSingleton<IExpressionCacheService, ExpressionCacheService>();

        return services;
    }

    /// <summary>
    /// Přidá Events služby.
    /// </summary>
    /// <param name="services">Service collection</param>
    /// <returns>Service collection pro fluent API</returns>
    public static IServiceCollection AddEvents(this IServiceCollection services)
    {
        services.AddScoped<IEventDefinitionService, EventDefinitionService>();

        return services;
    }

    /// <summary>
    /// Přidá společné služby.
    /// </summary>
    /// <param name="services">Service collection</param>
    /// <returns>Service collection pro fluent API</returns>
    public static IServiceCollection AddCommonServices(this IServiceCollection services)
    {
        // Registrace entity type map pro BusinessAutomation - zatím prázdná
        services.AddSingleton<IReadOnlyDictionary<string, Type>>(provider =>
        {
            var entityTypeMap = new Dictionary<string, Type>();

            // Pro BusinessAutomation systém registrujeme pouze vlastní entity
            entityTypeMap["Expression"] = typeof(BusinessAutomation.Core.Abstractions.ExpressionEngine.Models.Expression);
            entityTypeMap["EventDefinition"] = typeof(BusinessAutomation.Core.Abstractions.Events.Models.EventDefinition);

            return entityTypeMap;
        });

        services.AddScoped<EntityMetadataService>();

        return services;
    }
}
