using BusinessAutomation.Core.Abstractions.Events.Models;
using BusinessAutomation.Core.Abstractions.Events.Services;
using BusinessAutomation.Core.Abstractions.ExpressionEngine.Models;
using BusinessAutomation.Core.Abstractions.ExpressionEngine.Services;
using Domain.Entities;
using BusinessAutomation.Core.Infrastructure.Events.Services;
using BusinessAutomation.Core.Infrastructure;
using BusinessAutomation.Core.Abstractions;
using BusinessAutomation.Core.Abstractions.Events;
using Microsoft.AspNetCore.Builder;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Routing;
using Microsoft.Extensions.DependencyInjection;
using SharedKernel.Domain;

namespace BusinessAutomation.Core.Infrastructure.Events.Endpoints;

/// <summary>
/// Jednoduchý endpoint pro testování vytváření definic událostí.
/// </summary>
public static class SimpleEventSeederEndpoints
{
    /// <summary>
    /// Registruje jednoduchý seeder endpoint.
    /// </summary>
    /// <param name="app">Web aplikace</param>
    /// <returns>Web aplikace pro fluent API</returns>
    public static WebApplication MapSimpleEventSeederEndpoints(this WebApplication app)
    {
        var group = app.MapGroup("/api/events/simple-seed")
            .WithTags("Simple Event Seeder");

        group.MapPost("/test", CreateTestEventDefinition)
            .WithName("CreateTestEventDefinition")
            .WithSummary("Vytvoří jednu testovací definici události")
            .Produces<TestSeedResult>(StatusCodes.Status200OK)
            .Produces<ProblemDetails>(StatusCodes.Status500InternalServerError);

        return app;
    }

    /// <summary>
    /// Vytvoří jednu testovací definici události.
    /// </summary>
    /// <param name="eventDefinitionService">Služba pro správu definic událostí</param>
    /// <param name="expressionRepository">Repository pro výrazy</param>
    /// <param name="cancellationToken">Token pro zrušení operace</param>
    /// <returns>Výsledek vytvoření</returns>
    private static async Task<IResult> CreateTestEventDefinition(
        EventDefinitionService eventDefinitionService,
        IExpressionRepository expressionRepository,
        CancellationToken cancellationToken)
    {
        try
        {
            // Nejprve vytvoříme jednoduchý Expression Engine výraz
            var expression = new Expression
            {
                Id = Guid.NewGuid(),
                Name = "TestSampleEntityIsActive",
                Description = "Test výraz pro kontrolu IsActive vlastnosti",
                ExpressionTree = new SourceValueNode
                {
                    SourcePath = nameof(SampleEntity.IsActive)
                },
                IsActive = true,
                Version = 1,
                EffectiveFrom = DateTime.UtcNow
            };

            // Uložíme výraz
            await expressionRepository.AddAsync(expression);

            // Pak vytvoříme definici události
            var eventDefinition = new EventDefinition(
                "TestSampleEntityActivated", 
                typeof(SampleEntity), 
                EventType.Updated)
            {
                Description = "Test událost při aktivaci SampleEntity",
                ConditionExpressionName = expression.Name
            };

            eventDefinition.WithTrackedProperties(nameof(SampleEntity.IsActive));

            // Uložíme definici události
            await eventDefinitionService.CreateAsync(eventDefinition, cancellationToken);

            return Results.Ok(new TestSeedResult
            {
                Success = true,
                Message = "Testovací definice události byla úspěšně vytvořena",
                ExpressionName = expression.Name,
                EventDefinitionName = eventDefinition.EventName
            });
        }
        catch (Exception ex)
        {
            return Results.Problem(
                title: "Chyba při vytváření testovací definice události",
                detail: ex.Message,
                statusCode: StatusCodes.Status500InternalServerError
            );
        }
    }
}

/// <summary>
/// Výsledek vytvoření testovací definice události.
/// </summary>
public class TestSeedResult
{
    /// <summary>
    /// Indikuje, zda bylo vytvoření úspěšné.
    /// </summary>
    public bool Success { get; set; }

    /// <summary>
    /// Zpráva o výsledku.
    /// </summary>
    public string Message { get; set; } = string.Empty;

    /// <summary>
    /// Název vytvořeného výrazu.
    /// </summary>
    public string? ExpressionName { get; set; }

    /// <summary>
    /// Název vytvořené definice události.
    /// </summary>
    public string? EventDefinitionName { get; set; }
}
