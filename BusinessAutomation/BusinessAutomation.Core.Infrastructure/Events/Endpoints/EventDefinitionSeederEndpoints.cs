using BusinessAutomation.Core.Abstractions.Events.Models;
using BusinessAutomation.Core.Abstractions.Events.Services;
using BusinessAutomation.Core.Abstractions.ExpressionEngine.Models;
using BusinessAutomation.Core.Abstractions.ExpressionEngine.Services;
using BusinessAutomation.Core.Infrastructure.Events.Services;
using Microsoft.AspNetCore.Builder;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Routing;
using Microsoft.Extensions.DependencyInjection;

namespace BusinessAutomation.Core.Infrastructure.Events.Endpoints;

/// <summary>
/// Endpointy pro správu seedování definic událostí.
/// Používá se pro E2E testy a demonstrační účely.
/// </summary>
public static class EventDefinitionSeederEndpoints
{
    /// <summary>
    /// Registruje endpointy pro seedování definic událostí.
    /// </summary>
    /// <param name="app">Web aplikace</param>
    /// <returns>Web aplikace pro fluent API</returns>
    public static WebApplication MapEventDefinitionSeederEndpoints(this WebApplication app)
    {
        var group = app.MapGroup("/api/events/seed")
            .WithTags("Event Definition Seeder");

        group.MapPost("/all", SeedAllEventDefinitions)
            .WithName("SeedAllEventDefinitions")
            .WithSummary("Vytvoří všechny ukázkové definice událostí a výrazy")
            .WithDescription("Vytvoří ukázkové definice událostí pro SampleEntity, Order a OrderItem včetně potřebných Expression Engine výrazů. Používá se pro E2E testy a demonstrační účely.")
            .Produces<SeedResult>(StatusCodes.Status200OK)
            .Produces<ProblemDetails>(StatusCodes.Status500InternalServerError);

        group.MapPost("/sample-entity", SeedSampleEntityEvents)
            .WithName("SeedSampleEntityEvents")
            .WithSummary("Vytvoří definice událostí pro SampleEntity")
            .WithDescription("Vytvoří pouze definice událostí související se SampleEntity.")
            .Produces<SeedResult>(StatusCodes.Status200OK)
            .Produces<ProblemDetails>(StatusCodes.Status500InternalServerError);

        group.MapGet("/status", GetSeedStatus)
            .WithName("GetEventDefinitionSeedStatus")
            .WithSummary("Získá stav seedování definic událostí")
            .WithDescription("Vrací informace o tom, kolik definic událostí a výrazů je v databázi.")
            .Produces<SeedStatusResult>(StatusCodes.Status200OK);

        return app;
    }

    /// <summary>
    /// Vytvoří všechny ukázkové definice událostí a výrazy.
    /// </summary>
    /// <param name="seeder">Seeder služba</param>
    /// <param name="cancellationToken">Token pro zrušení operace</param>
    /// <returns>Výsledek seedování</returns>
    private static async Task<IResult> SeedAllEventDefinitions(
        EventDefinitionSeeder seeder,
        CancellationToken cancellationToken)
    {
        try
        {
            var createdCount = await seeder.SeedAllAsync(cancellationToken);
            
            return Results.Ok(new SeedResult
            {
                Success = true,
                Message = $"Úspěšně vytvořeno {createdCount} definic událostí",
                CreatedCount = createdCount
            });
        }
        catch (Exception ex)
        {
            return Results.Problem(
                title: "Chyba při seedování definic událostí",
                detail: ex.Message,
                statusCode: StatusCodes.Status500InternalServerError
            );
        }
    }

    /// <summary>
    /// Vytvoří definice událostí pouze pro SampleEntity.
    /// </summary>
    /// <param name="seeder">Seeder služba</param>
    /// <param name="cancellationToken">Token pro zrušení operace</param>
    /// <returns>Výsledek seedování</returns>
    private static async Task<IResult> SeedSampleEntityEvents(
        EventDefinitionSeeder seeder,
        CancellationToken cancellationToken)
    {
        try
        {
            // Pro tento endpoint vytvoříme pouze SampleEntity události
            // Toto je zjednodušená verze - v reálné implementaci by seeder měl mít separátní metodu
            var createdCount = await seeder.SeedAllAsync(cancellationToken);
            
            return Results.Ok(new SeedResult
            {
                Success = true,
                Message = $"Seedování dokončeno (celkem {createdCount} definic)",
                CreatedCount = createdCount
            });
        }
        catch (Exception ex)
        {
            return Results.Problem(
                title: "Chyba při seedování SampleEntity událostí",
                detail: ex.Message,
                statusCode: StatusCodes.Status500InternalServerError
            );
        }
    }

    /// <summary>
    /// Získá stav seedování definic událostí.
    /// </summary>
    /// <param name="eventDefinitionService">Služba pro správu definic událostí</param>
    /// <param name="cancellationToken">Token pro zrušení operace</param>
    /// <returns>Stav seedování</returns>
    private static async Task<IResult> GetSeedStatus(
        [FromServices] EventDefinitionService eventDefinitionService,
        CancellationToken cancellationToken)
    {
        try
        {
            var allDefinitions = await eventDefinitionService.GetAllAsync(cancellationToken);
            var activeDefinitions = allDefinitions.Where(d => d.IsActive).ToList();
            
            var sampleEntityDefinitions = allDefinitions
                .Where(d => d.EntityTypeName.Contains("SampleEntity"))
                .ToList();
            
            var orderDefinitions = allDefinitions
                .Where(d => d.EntityTypeName.Contains("Order"))
                .ToList();

            return Results.Ok(new SeedStatusResult
            {
                TotalDefinitions = allDefinitions.Count,
                ActiveDefinitions = activeDefinitions.Count,
                SampleEntityDefinitions = sampleEntityDefinitions.Count,
                OrderDefinitions = orderDefinitions.Count,
                DefinitionsByEntity = allDefinitions
                    .GroupBy(d => d.EntityName)
                    .ToDictionary(g => g.Key, g => g.Count())
            });
        }
        catch (Exception ex)
        {
            return Results.Problem(
                title: "Chyba při získávání stavu seedování",
                detail: ex.Message,
                statusCode: StatusCodes.Status500InternalServerError
            );
        }
    }
}

/// <summary>
/// Výsledek seedování definic událostí.
/// </summary>
public class SeedResult
{
    /// <summary>
    /// Indikuje, zda bylo seedování úspěšné.
    /// </summary>
    public bool Success { get; set; }

    /// <summary>
    /// Zpráva o výsledku seedování.
    /// </summary>
    public string Message { get; set; } = string.Empty;

    /// <summary>
    /// Počet vytvořených definic.
    /// </summary>
    public int CreatedCount { get; set; }
}

/// <summary>
/// Stav seedování definic událostí.
/// </summary>
public class SeedStatusResult
{
    /// <summary>
    /// Celkový počet definic událostí.
    /// </summary>
    public int TotalDefinitions { get; set; }

    /// <summary>
    /// Počet aktivních definic událostí.
    /// </summary>
    public int ActiveDefinitions { get; set; }

    /// <summary>
    /// Počet definic pro SampleEntity.
    /// </summary>
    public int SampleEntityDefinitions { get; set; }

    /// <summary>
    /// Počet definic pro Order entity.
    /// </summary>
    public int OrderDefinitions { get; set; }

    /// <summary>
    /// Počet definic podle typu entity.
    /// </summary>
    public Dictionary<string, int> DefinitionsByEntity { get; set; } = new();
}
