using BusinessAutomation.Core.Abstractions.Events.Models;
using BusinessAutomation.Core.Abstractions.Events.Services;
using BusinessAutomation.Core.Abstractions.ExpressionEngine.Models;
using BusinessAutomation.Core.Abstractions.ExpressionEngine.Services;
using BusinessAutomation.Core.Abstractions.ExpressionEngine.Models;
using BusinessAutomation.Core.Infrastructure.ExpressionEngine.Core;
using Microsoft.Extensions.Logging;

namespace BusinessAutomation.Core.Infrastructure.Events.Services;

/// <summary>
/// Služba pro vyhodnocování podmínek událostí pomocí Expression Engine.
/// Umožňuje vyhodnotit boolean výrazy definované v Expression Engine pro určení,
/// zda má být událost spuštěna.
/// </summary>
public class EventConditionEvaluator
{
    private readonly CalculationEngine _calculationEngine;
    private readonly IExpressionRepository _expressionRepository;
    private readonly ILogger<EventConditionEvaluator> _logger;

    /// <summary>
    /// Konstruktor evaluátoru podmínek událostí.
    /// </summary>
    /// <param name="calculationEngine">Engine pro vyhodnocování výrazů</param>
    /// <param name="expressionRepository">Repository pro přístup k výrazům</param>
    /// <param name="logger">Logger</param>
    public EventConditionEvaluator(
        CalculationEngine calculationEngine,
        IExpressionRepository expressionRepository,
        ILogger<EventConditionEvaluator> logger)
    {
        _calculationEngine = calculationEngine;
        _expressionRepository = expressionRepository;
        _logger = logger;
    }

    /// <summary>
    /// Vyhodnotí podmínku události pro danou entitu.
    /// </summary>
    /// <param name="conditionExpressionName">Název výrazu pro podmínku</param>
    /// <param name="entity">Entita, na které se podmínka vyhodnocuje</param>
    /// <param name="cancellationToken">Token pro zrušení operace</param>
    /// <returns>True pokud je podmínka splněna, false pokud ne, null pokud podmínka není definována</returns>
    public async Task<bool?> EvaluateConditionAsync(
        string? conditionExpressionName, 
        object entity, 
        CancellationToken cancellationToken = default)
    {
        // Pokud není podmínka definována, událost se spouští vždy
        if (string.IsNullOrWhiteSpace(conditionExpressionName))
        {
            _logger.LogTrace("Podmínka není definována - událost se spustí");
            return null; // Indikuje, že podmínka není definována
        }

        if (entity == null)
        {
            _logger.LogWarning("Entita je null - podmínka nemůže být vyhodnocena");
            return false;
        }

        try
        {
            // Získáme výraz z repository
            var expression = await _expressionRepository.GetByNameAsync(conditionExpressionName);
            if (expression == null)
            {
                _logger.LogWarning("Výraz '{ExpressionName}' nebyl nalezen - podmínka se považuje za nesplněnou", 
                    conditionExpressionName);
                return false;
            }

            if (!expression.IsActive)
            {
                _logger.LogWarning("Výraz '{ExpressionName}' není aktivní - podmínka se považuje za nesplněnou", 
                    conditionExpressionName);
                return false;
            }

            // Vyhodnotíme výraz
            var result = await _calculationEngine.ExecuteAsync(expression, entity);
            
            // Ověříme, že výsledek je boolean
            if (result is bool boolResult)
            {
                _logger.LogTrace("Podmínka '{ExpressionName}' vyhodnocena jako {Result}", 
                    conditionExpressionName, boolResult);
                return boolResult;
            }

            _logger.LogWarning("Výraz '{ExpressionName}' nevrátil boolean hodnotu (vrátil {ResultType}) - podmínka se považuje za nesplněnou", 
                conditionExpressionName, result?.GetType().Name ?? "null");
            return false;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Chyba při vyhodnocování podmínky '{ExpressionName}' - podmínka se považuje za nesplněnou", 
                conditionExpressionName);
            return false;
        }
    }

    /// <summary>
    /// Ověří, zda výraz s daným názvem existuje a je aktivní.
    /// </summary>
    /// <param name="conditionExpressionName">Název výrazu</param>
    /// <param name="cancellationToken">Token pro zrušení operace</param>
    /// <returns>True pokud výraz existuje a je aktivní</returns>
    public async Task<bool> IsValidConditionExpressionAsync(
        string? conditionExpressionName, 
        CancellationToken cancellationToken = default)
    {
        if (string.IsNullOrWhiteSpace(conditionExpressionName))
            return true; // Prázdná podmínka je validní

        try
        {
            var expression = await _expressionRepository.GetByNameAsync(conditionExpressionName);
            return expression != null && expression.IsActive;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Chyba při ověřování validity výrazu '{ExpressionName}'", conditionExpressionName);
            return false;
        }
    }

    /// <summary>
    /// Získá seznam všech dostupných boolean výrazů pro použití jako podmínky událostí.
    /// </summary>
    /// <param name="entityName">Název entity pro filtrování výrazů (volitelné)</param>
    /// <param name="cancellationToken">Token pro zrušení operace</param>
    /// <returns>Seznam názvů dostupných boolean výrazů</returns>
    public async Task<IEnumerable<string>> GetAvailableBooleanExpressionsAsync(
        string? entityName = null, 
        CancellationToken cancellationToken = default)
    {
        try
        {
            // Získáme všechny aktivní výrazy
            var expressions = await _expressionRepository.GetAllAsync();

            // Filtrujeme pouze výrazy, které by mohly vracet boolean
            // (v ideálním případě by Expression měla mít informaci o návratovém typu)
            return expressions
                .Where(e => e.IsActive)
                .Select(e => e.Name)
                .OrderBy(name => name)
                .ToList();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Chyba při získávání dostupných boolean výrazů pro entitu '{EntityName}'", entityName);
            return Enumerable.Empty<string>();
        }
    }

    /// <summary>
    /// Vytvoří jednoduchý boolean výraz pro testovací účely.
    /// </summary>
    /// <param name="expressionName">Název výrazu</param>
    /// <param name="entityName">Název cílové entity</param>
    /// <param name="propertyPath">Cesta k boolean vlastnosti (např. "IsActive")</param>
    /// <param name="description">Popis výrazu</param>
    /// <returns>Vytvořený výraz</returns>
    public BusinessAutomation.Core.Abstractions.ExpressionEngine.Models.Expression CreateSimpleBooleanExpression(
        string expressionName,
        string entityName,
        string propertyPath,
        string? description = null)
    {
        return new BusinessAutomation.Core.Abstractions.ExpressionEngine.Models.Expression
        {
            Id = Guid.NewGuid(),
            Name = expressionName,
            Description = description ?? $"Boolean výraz pro vlastnost {propertyPath}",
            ExpressionTree = new SourceValueNode
            {
                SourcePath = propertyPath
            },
            IsActive = true,
            Version = 1,
            EffectiveFrom = DateTime.UtcNow
        };
    }

    /// <summary>
    /// Vytvoří složitější boolean výraz s porovnáním.
    /// </summary>
    /// <param name="expressionName">Název výrazu</param>
    /// <param name="entityName">Název cílové entity</param>
    /// <param name="propertyPath">Cesta k vlastnosti</param>
    /// <param name="operatorType">Typ operátoru porovnání</param>
    /// <param name="compareValue">Hodnota pro porovnání</param>
    /// <param name="valueType">Typ hodnoty</param>
    /// <param name="description">Popis výrazu</param>
    /// <returns>Vytvořený výraz</returns>
    public BusinessAutomation.Core.Abstractions.ExpressionEngine.Models.Expression CreateComparisonBooleanExpression(
        string expressionName,
        string entityName,
        string propertyPath,
        OperatorType operatorType,
        string compareValue,
        BusinessAutomation.Core.Abstractions.ExpressionEngine.Models.ValueType valueType,
        string? description = null)
    {
        return new BusinessAutomation.Core.Abstractions.ExpressionEngine.Models.Expression
        {
            Id = Guid.NewGuid(),
            Name = expressionName,
            Description = description ?? $"Porovnání {propertyPath} {operatorType} {compareValue}",
            ExpressionTree = new OperationNode
            {
                Operator = operatorType,
                Operands = new List<ExpressionNode>
                {
                    new SourceValueNode { SourcePath = propertyPath },
                    new ConstantNode { DataType = valueType, Value = compareValue }
                }
            },
            IsActive = true,
            Version = 1,
            EffectiveFrom = DateTime.UtcNow
        };
    }
}
