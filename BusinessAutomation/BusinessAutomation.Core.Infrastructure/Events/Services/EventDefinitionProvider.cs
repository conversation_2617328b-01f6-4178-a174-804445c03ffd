using BusinessAutomation.Core.Abstractions.Events.Models;
using BusinessAutomation.Core.Abstractions.Events.Services;
using BusinessAutomation.Core.Abstractions.ExpressionEngine.Models;
using BusinessAutomation.Core.Abstractions.ExpressionEngine.Services;
using BusinessAutomation.Core.Abstractions.Events;
using Microsoft.Extensions.Logging;
using SharedKernel.Domain;
using System.Collections.Concurrent;

namespace BusinessAutomation.Core.Infrastructure.Events.Services;

/// <summary>
/// Poskytovatel definic událostí s cache funkcionalitou.
/// Kombinuje databázovou perzistenci s rychlým přístupem přes cache.
/// </summary>
public class EventDefinitionProvider : IEventDefinitionProvider
{
    private readonly EventDefinitionService _eventDefinitionService;
    private readonly ILogger<EventDefinitionProvider> _logger;
    private readonly ConcurrentDictionary<string, List<EventDefinition>> _cache = new();
    private readonly SemaphoreSlim _refreshSemaphore = new(1, 1);
    private DateTime _lastRefresh = DateTime.MinValue;
    private readonly TimeSpan _cacheExpiry = TimeSpan.FromMinutes(5); // Cache expiruje po 5 minutách

    /// <summary>
    /// Konstruktor provideru.
    /// </summary>
    /// <param name="eventDefinitionService">Služba pro práci s databází</param>
    /// <param name="logger">Logger</param>
    public EventDefinitionProvider(EventDefinitionService eventDefinitionService, ILogger<EventDefinitionProvider> logger)
    {
        _eventDefinitionService = eventDefinitionService;
        _logger = logger;
    }

    /// <summary>
    /// Získá všechny aktivní definice událostí pro daný typ entity a operaci.
    /// </summary>
    /// <param name="entityType">Typ entity</param>
    /// <param name="operation">Operace</param>
    /// <param name="cancellationToken">Token pro zrušení operace</param>
    /// <returns>Kolekce definic událostí</returns>
    public async Task<IEnumerable<EventDefinition>> GetDefinitionsAsync(Type entityType, EventType operation, CancellationToken cancellationToken = default)
    {
        if (entityType == null)
            return Enumerable.Empty<EventDefinition>();

        await EnsureCacheIsCurrentAsync(cancellationToken);

        var entityTypeName = entityType.AssemblyQualifiedName ?? entityType.FullName ?? entityType.Name;
        var cacheKey = $"{entityTypeName}:{operation}";

        if (_cache.TryGetValue(cacheKey, out var cachedDefinitions))
        {
            return cachedDefinitions;
        }

        // Pokud není v cache, načteme z databáze a uložíme do cache
        var definitions = await _eventDefinitionService.GetByEntityTypeAndOperationAsync(entityTypeName, operation, cancellationToken);
        var definitionsList = definitions.ToList();
        
        _cache.TryAdd(cacheKey, definitionsList);
        
        return definitionsList;
    }

    /// <summary>
    /// Získá všechny aktivní definice událostí pro daný typ entity.
    /// </summary>
    /// <param name="entityType">Typ entity</param>
    /// <param name="cancellationToken">Token pro zrušení operace</param>
    /// <returns>Kolekce definic událostí</returns>
    public async Task<IEnumerable<EventDefinition>> GetDefinitionsAsync(Type entityType, CancellationToken cancellationToken = default)
    {
        if (entityType == null)
            return Enumerable.Empty<EventDefinition>();

        await EnsureCacheIsCurrentAsync(cancellationToken);

        var entityTypeName = entityType.AssemblyQualifiedName ?? entityType.FullName ?? entityType.Name;
        var cacheKey = $"{entityTypeName}:All";

        if (_cache.TryGetValue(cacheKey, out var cachedDefinitions))
        {
            return cachedDefinitions;
        }

        // Pokud není v cache, načteme z databáze a uložíme do cache
        var definitions = await _eventDefinitionService.GetByEntityTypeAsync(entityTypeName, cancellationToken);
        var definitionsList = definitions.ToList();
        
        _cache.TryAdd(cacheKey, definitionsList);
        
        return definitionsList;
    }

    /// <summary>
    /// Obnoví cache definic událostí z databáze.
    /// </summary>
    /// <param name="cancellationToken">Token pro zrušení operace</param>
    public async Task RefreshCacheAsync(CancellationToken cancellationToken = default)
    {
        await _refreshSemaphore.WaitAsync(cancellationToken);
        try
        {
            _logger.LogDebug("Obnovuji cache definic událostí...");
            
            _cache.Clear();
            _lastRefresh = DateTime.UtcNow;
            
            // Předem načteme všechny aktivní definice a připravíme cache
            var allDefinitions = await _eventDefinitionService.GetActiveAsync(cancellationToken);
            
            // Seskupíme podle typu entity a operace
            var groupedDefinitions = allDefinitions
                .GroupBy(d => new { d.EntityTypeName, d.Operation })
                .ToList();

            foreach (var group in groupedDefinitions)
            {
                var cacheKey = $"{group.Key.EntityTypeName}:{group.Key.Operation}";
                _cache.TryAdd(cacheKey, group.ToList());
            }

            // Také seskupíme podle typu entity (všechny operace)
            var entityGroupedDefinitions = allDefinitions
                .GroupBy(d => d.EntityTypeName)
                .ToList();

            foreach (var group in entityGroupedDefinitions)
            {
                var cacheKey = $"{group.Key}:All";
                _cache.TryAdd(cacheKey, group.ToList());
            }

            _logger.LogDebug("Cache definic událostí obnovena. Načteno {Count} definic do {CacheCount} cache klíčů.", 
                allDefinitions.Count, _cache.Count);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Chyba při obnovování cache definic událostí");
            throw;
        }
        finally
        {
            _refreshSemaphore.Release();
        }
    }

    /// <summary>
    /// Zajistí, že cache je aktuální.
    /// </summary>
    /// <param name="cancellationToken">Token pro zrušení operace</param>
    private async Task EnsureCacheIsCurrentAsync(CancellationToken cancellationToken)
    {
        var now = DateTime.UtcNow;
        if (now - _lastRefresh > _cacheExpiry || _cache.IsEmpty)
        {
            await RefreshCacheAsync(cancellationToken);
        }
    }
}
