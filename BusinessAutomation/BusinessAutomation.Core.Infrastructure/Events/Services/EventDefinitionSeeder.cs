using BusinessAutomation.Core.Abstractions.Events.Models;
using BusinessAutomation.Core.Abstractions.Events.Services;
using BusinessAutomation.Core.Abstractions.ExpressionEngine.Models;
using BusinessAutomation.Core.Abstractions.ExpressionEngine.Services;
using Domain.Entities;
using BusinessAutomation.Core.Abstractions.Events;
using BusinessAutomation.Core.Abstractions;
using BusinessAutomation.Core.Infrastructure.Events.Services;
using Microsoft.Extensions.Logging;
using SharedKernel.Domain;

namespace BusinessAutomation.Core.Infrastructure.Events.Services;

/// <summary>
/// Služba pro vytvoření ukázkových definic událostí a souvisejících výrazů v databázi.
/// Používá se pro E2E testy a demonstrační ú<PERSON>.
/// </summary>
public class EventDefinitionSeeder
{
    private readonly EventDefinitionService _eventDefinitionService;
    private readonly IExpressionRepository _expressionRepository;
    private readonly EventConditionEvaluator _conditionEvaluator;
    private readonly ILogger<EventDefinitionSeeder> _logger;

    /// <summary>
    /// Konstruktor seederu.
    /// </summary>
    /// <param name="eventDefinitionService">Služba pro správu definic událostí</param>
    /// <param name="expressionRepository">Repository pro výrazy</param>
    /// <param name="conditionEvaluator">Evaluátor podmínek</param>
    /// <param name="logger">Logger</param>
    public EventDefinitionSeeder(
        EventDefinitionService eventDefinitionService,
        IExpressionRepository expressionRepository,
        EventConditionEvaluator conditionEvaluator,
        ILogger<EventDefinitionSeeder> logger)
    {
        _eventDefinitionService = eventDefinitionService;
        _expressionRepository = expressionRepository;
        _conditionEvaluator = conditionEvaluator;
        _logger = logger;
    }

    /// <summary>
    /// Vytvoří všechny ukázkové definice událostí a výrazy v databázi.
    /// </summary>
    /// <param name="cancellationToken">Token pro zrušení operace</param>
    /// <returns>Počet vytvořených definic</returns>
    public async Task<int> SeedAllAsync(CancellationToken cancellationToken = default)
    {
        _logger.LogInformation("Začínám vytváření ukázkových definic událostí a výrazů...");

        var createdCount = 0;

        try
        {
            // Nejprve vytvoříme Expression Engine výrazy
            await CreateExpressionEngineExpressionsAsync(cancellationToken);

            // Pak vytvoříme definice událostí
            createdCount += await CreateSampleEntityEventsAsync(cancellationToken);
            createdCount += await CreateOrderEventsAsync(cancellationToken);
            createdCount += await CreateOrderItemEventsAsync(cancellationToken);

            _logger.LogInformation("Úspěšně vytvořeno {Count} definic událostí", createdCount);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Chyba při vytváření ukázkových definic událostí");
            throw;
        }

        return createdCount;
    }

    /// <summary>
    /// Vytvoří Expression Engine výrazy pro podmínky událostí.
    /// </summary>
    /// <param name="cancellationToken">Token pro zrušení operace</param>
    private async Task CreateExpressionEngineExpressionsAsync(CancellationToken cancellationToken)
    {
        _logger.LogDebug("Vytváření Expression Engine výrazů...");

        var expressions = new[]
        {
            // SampleEntity výrazy
            _conditionEvaluator.CreateSimpleBooleanExpression(
                "SampleEntityIsActiveCheck",
                nameof(SampleEntity),
                nameof(SampleEntity.IsActive),
                "Kontrola zda je SampleEntity aktivní"
            ),

            _conditionEvaluator.CreateComparisonBooleanExpression(
                "SampleEntityAgeMilestoneCheck",
                nameof(SampleEntity),
                nameof(SampleEntity.Age),
                OperatorType.Equal,
                "18",
                BusinessAutomation.Core.Abstractions.ExpressionEngine.Models.ValueType.Integer,
                "Kontrola zda je SampleEntity 18 let (věkový milník)"
            ),

            _conditionEvaluator.CreateComparisonBooleanExpression(
                "SampleEntityIsAdultCheck",
                nameof(SampleEntity),
                nameof(SampleEntity.Age),
                OperatorType.GreaterThanOrEqual,
                "18",
                BusinessAutomation.Core.Abstractions.ExpressionEngine.Models.ValueType.Integer,
                "Kontrola zda je SampleEntity dospělá (věk >= 18)"
            ),

            // Order výrazy
            _conditionEvaluator.CreateComparisonBooleanExpression(
                "OrderIsHighValueCheck",
                nameof(Order),
                nameof(Order.TotalAmount),
                OperatorType.GreaterThan,
                "50000",
                BusinessAutomation.Core.Abstractions.ExpressionEngine.Models.ValueType.Decimal,
                "Kontrola zda je objednávka vysoké hodnoty (> 50 000)"
            ),

            _conditionEvaluator.CreateComparisonBooleanExpression(
                "OrderHasDiscountCheck",
                nameof(Order),
                nameof(Order.DiscountPercentage),
                OperatorType.GreaterThan,
                "0",
                BusinessAutomation.Core.Abstractions.ExpressionEngine.Models.ValueType.Decimal,
                "Kontrola zda má objednávka slevu"
            )
        };

        foreach (var expression in expressions)
        {
            try
            {
                // Zkontrolujeme, zda výraz již existuje
                var existing = await _expressionRepository.GetByNameAsync(expression.Name);
                if (existing == null)
                {
                    await _expressionRepository.AddAsync(expression);
                    _logger.LogDebug("Vytvořen výraz: {ExpressionName}", expression.Name);
                }
                else
                {
                    _logger.LogDebug("Výraz již existuje: {ExpressionName}", expression.Name);
                }
            }
            catch (Exception ex)
            {
                _logger.LogWarning(ex, "Nepodařilo se vytvořit výraz: {ExpressionName}", expression.Name);
            }
        }
    }

    /// <summary>
    /// Vytvoří definice událostí pro SampleEntity.
    /// </summary>
    /// <param name="cancellationToken">Token pro zrušení operace</param>
    /// <returns>Počet vytvořených definic</returns>
    private async Task<int> CreateSampleEntityEventsAsync(CancellationToken cancellationToken)
    {
        _logger.LogDebug("Vytváření definic událostí pro SampleEntity...");

        var definitions = new[]
        {
            // Základní CRUD události
            new EventDefinition("SampleEntityCreated", typeof(SampleEntity), EventType.Created)
                .WithDescription("Událost při vytvoření nové SampleEntity"),

            new EventDefinition("SampleEntityDeleted", typeof(SampleEntity), EventType.Deleted)
                .WithDescription("Událost při smazání SampleEntity"),

            // Události při změnách konkrétních vlastností
            new EventDefinition("SampleEntityNameChanged", typeof(SampleEntity), EventType.Updated)
                .WithTrackedProperties(nameof(SampleEntity.Name))
                .WithDescription("Událost při změně názvu SampleEntity"),

            new EventDefinition("SampleEntityActivated", typeof(SampleEntity), EventType.Updated)
                .WithTrackedProperties(nameof(SampleEntity.IsActive))
                .WithCondition("SampleEntityIsActiveCheck")
                .WithDescription("Událost při aktivaci SampleEntity"),

            new EventDefinition("SampleEntityAgeChanged", typeof(SampleEntity), EventType.Updated)
                .WithTrackedProperties(nameof(SampleEntity.Age))
                .WithDescription("Událost při změně věku SampleEntity"),

            new EventDefinition("SampleEntityAgeMilestone", typeof(SampleEntity), EventType.Updated)
                .WithTrackedProperties(nameof(SampleEntity.Age))
                .WithCondition("SampleEntityAgeMilestoneCheck")
                .WithDescription("Událost při dosažení věkového milníku"),

            new EventDefinition("SampleEntityBecameAdult", typeof(SampleEntity), EventType.Updated)
                .WithTrackedProperties(nameof(SampleEntity.Age))
                .WithCondition("SampleEntityIsAdultCheck")
                .WithDescription("Událost při dosažení dospělosti (18 let)"),

            new EventDefinition("SampleEntityDescriptionChanged", typeof(SampleEntity), EventType.Updated)
                .WithTrackedProperties(nameof(SampleEntity.Description))
                .WithDescription("Událost při změně popisu SampleEntity"),

            new EventDefinition("SampleEntityDateOfBirthChanged", typeof(SampleEntity), EventType.Updated)
                .WithTrackedProperties(nameof(SampleEntity.DateOfBirth))
                .WithDescription("Událost při změně data narození SampleEntity"),

            // Složitější události
            new EventDefinition("SampleEntityProfileUpdated", typeof(SampleEntity), EventType.Updated)
                .WithTrackedProperties(nameof(SampleEntity.Name), nameof(SampleEntity.Description), 
                                     nameof(SampleEntity.Age), nameof(SampleEntity.DateOfBirth))
                .WithDescription("Událost při změně profilu SampleEntity (více vlastností)"),

            new EventDefinition("SampleEntityAnyChange", typeof(SampleEntity), EventType.Updated)
                .WithDescription("Univerzální událost při jakékoliv změně SampleEntity")
        };

        return await CreateEventDefinitionsAsync(definitions, cancellationToken);
    }

    /// <summary>
    /// Vytvoří definice událostí pro Order.
    /// </summary>
    /// <param name="cancellationToken">Token pro zrušení operace</param>
    /// <returns>Počet vytvořených definic</returns>
    private async Task<int> CreateOrderEventsAsync(CancellationToken cancellationToken)
    {
        _logger.LogDebug("Vytváření definic událostí pro Order...");

        var definitions = new[]
        {
            new EventDefinition("OrderCreated", typeof(Order), EventType.Created)
                .WithDescription("Událost při vytvoření nové objednávky"),

            new EventDefinition("OrderStatusChanged", typeof(Order), EventType.Updated)
                .WithTrackedProperties(nameof(Order.Status))
                .WithDescription("Událost při změně stavu objednávky"),

            new EventDefinition("OrderHighValueCreated", typeof(Order), EventType.Created)
                .WithCondition("OrderIsHighValueCheck")
                .WithDescription("Událost při vytvoření objednávky vysoké hodnoty"),

            new EventDefinition("OrderDiscountApplied", typeof(Order), EventType.Updated)
                .WithTrackedProperties(nameof(Order.DiscountPercentage), nameof(Order.DiscountAmount))
                .WithCondition("OrderHasDiscountCheck")
                .WithDescription("Událost při aplikaci slevy na objednávku"),

            new EventDefinition("OrderTotalChanged", typeof(Order), EventType.Updated)
                .WithTrackedProperties(nameof(Order.TotalAmount))
                .WithDescription("Událost při změně celkové částky objednávky"),

            new EventDefinition("OrderDeleted", typeof(Order), EventType.Deleted)
                .WithDescription("Událost při smazání objednávky")
        };

        return await CreateEventDefinitionsAsync(definitions, cancellationToken);
    }

    /// <summary>
    /// Vytvoří definice událostí pro OrderItem.
    /// </summary>
    /// <param name="cancellationToken">Token pro zrušení operace</param>
    /// <returns>Počet vytvořených definic</returns>
    private async Task<int> CreateOrderItemEventsAsync(CancellationToken cancellationToken)
    {
        _logger.LogDebug("Vytváření definic událostí pro OrderItem...");

        var definitions = new[]
        {
            new EventDefinition("OrderItemCreated", typeof(OrderItem), EventType.Created)
                .WithDescription("Událost při přidání položky do objednávky"),

            new EventDefinition("OrderItemQuantityChanged", typeof(OrderItem), EventType.Updated)
                .WithTrackedProperties(nameof(OrderItem.Quantity))
                .WithDescription("Událost při změně množství položky"),

            new EventDefinition("OrderItemPriceChanged", typeof(OrderItem), EventType.Updated)
                .WithTrackedProperties(nameof(OrderItem.UnitPrice))
                .WithDescription("Událost při změně ceny položky"),

            new EventDefinition("OrderItemDeleted", typeof(OrderItem), EventType.Deleted)
                .WithDescription("Událost při odebrání položky z objednávky")
        };

        return await CreateEventDefinitionsAsync(definitions, cancellationToken);
    }

    /// <summary>
    /// Pomocná metoda pro vytvoření definic událostí v databázi.
    /// </summary>
    /// <param name="definitions">Definice k vytvoření</param>
    /// <param name="cancellationToken">Token pro zrušení operace</param>
    /// <returns>Počet vytvořených definic</returns>
    private async Task<int> CreateEventDefinitionsAsync(EventDefinition[] definitions, CancellationToken cancellationToken)
    {
        var createdCount = 0;

        foreach (var definition in definitions)
        {
            try
            {
                // Zkontrolujeme, zda definice již existuje
                var existing = await _eventDefinitionService.GetByNameAsync(definition.EventName, cancellationToken);
                if (existing == null)
                {
                    await _eventDefinitionService.CreateAsync(definition, cancellationToken);
                    createdCount++;
                    _logger.LogDebug("Vytvořena definice události: {EventName}", definition.EventName);
                }
                else
                {
                    _logger.LogDebug("Definice události již existuje: {EventName}", definition.EventName);
                }
            }
            catch (Exception ex)
            {
                _logger.LogWarning(ex, "Nepodařilo se vytvořit definici události: {EventName}", definition.EventName);
            }
        }

        return createdCount;
    }
}
