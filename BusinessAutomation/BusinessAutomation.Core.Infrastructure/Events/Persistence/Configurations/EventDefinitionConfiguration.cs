using BusinessAutomation.Core.Abstractions.Events.Models;
using BusinessAutomation.Core.Abstractions.Events.Services;
using BusinessAutomation.Core.Abstractions.ExpressionEngine.Models;
using BusinessAutomation.Core.Abstractions.ExpressionEngine.Services;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using BusinessAutomation.Core.Abstractions.Events;

namespace BusinessAutomation.Core.Infrastructure.Events.Persistence.Configurations;

/// <summary>
/// Konfigurace entity EventDefinition pro Entity Framework Core.
/// </summary>
public class EventDefinitionConfiguration : IEntityTypeConfiguration<EventDefinition>
{
    /// <summary>
    /// Konfiguruje mapování entity EventDefinition na databázovou tabulku.
    /// </summary>
    /// <param name="builder">Builder pro konfiguraci entity.</param>
    public void Configure(EntityTypeBuilder<EventDefinition> builder)
    {
        // Název tabulky
        builder.ToTable("EventDefinitions");

        // Primární klíč
        builder.HasKey(e => e.Id);
        builder.Property(e => e.Id)
            .ValueGeneratedOnAdd();

        // EventName - jedinečný název události
        builder.Property(e => e.EventName)
            .IsRequired()
            .HasMaxLength(200)
            .HasComment("Jedinečný název události");

        // EntityTypeName - název typu entity pro databázi
        builder.Property(e => e.EntityTypeName)
            .IsRequired()
            .HasMaxLength(500)
            .HasComment("Plný název typu entity včetně namespace");

        // Operation - typ operace
        builder.Property(e => e.Operation)
            .IsRequired()
            .HasConversion<string>()
            .HasMaxLength(50)
            .HasComment("Typ operace při které se událost spouští");

        // TrackedPropertiesJson - sledované vlastnosti jako JSON
        builder.Property(e => e.TrackedPropertiesJson)
            .IsRequired()
            .HasMaxLength(2000)
            .HasDefaultValue("[]")
            .HasComment("Seznam sledovaných vlastností serializovaný jako JSON");

        // ConditionExpressionName - název výrazu z Expression Engine
        builder.Property(e => e.ConditionExpressionName)
            .HasMaxLength(200)
            .HasComment("Název výrazu z Expression Engine pro podmínku spuštění události");

        // Description - popis události
        builder.Property(e => e.Description)
            .HasMaxLength(1000)
            .HasComment("Popis události pro dokumentační účely");

        // IsActive - indikuje aktivní stav
        builder.Property(e => e.IsActive)
            .IsRequired()
            .HasDefaultValue(true)
            .HasComment("Indikuje, zda je definice aktivní");

        // CreatedAt - datum vytvoření
        builder.Property(e => e.CreatedAt)
            .IsRequired()
            .HasComment("Datum a čas vytvoření definice");

        // RowVersion pro optimistické zamykání
        builder.Property(e => e.RowVersion)
            .IsRequired()
            .IsRowVersion()
            .HasDefaultValue(new byte[] { 0, 0, 0, 0, 0, 0, 0, 1 })
            .HasComment("Verze řádku pro optimistické zamykání");

        // Ignorujeme vlastnosti, které se nepersistují
        builder.Ignore(e => e.EntityType);
        builder.Ignore(e => e.TrackedProperties);
        builder.Ignore(e => e.EntityName);

        // Indexy
        builder.HasIndex(e => e.EventName)
            .IsUnique()
            .HasDatabaseName("IX_EventDefinitions_EventName_Unique");

        builder.HasIndex(e => e.EntityTypeName)
            .HasDatabaseName("IX_EventDefinitions_EntityTypeName");

        builder.HasIndex(e => new { e.EntityTypeName, e.Operation })
            .HasDatabaseName("IX_EventDefinitions_EntityTypeName_Operation");

        builder.HasIndex(e => e.IsActive)
            .HasDatabaseName("IX_EventDefinitions_IsActive");

        // Check constraints
        builder.HasCheckConstraint("CK_EventDefinitions_EventName_NotEmpty",
            "[EventName] IS NOT NULL AND LENGTH(TRIM([EventName])) > 0");

        builder.HasCheckConstraint("CK_EventDefinitions_EntityTypeName_NotEmpty",
            "[EntityTypeName] IS NOT NULL AND LENGTH(TRIM([EntityTypeName])) > 0");
    }
}
