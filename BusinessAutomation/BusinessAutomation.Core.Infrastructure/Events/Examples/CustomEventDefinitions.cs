using BusinessAutomation.Core.Abstractions.Events.Models;
using BusinessAutomation.Core.Abstractions.Events.Services;
using BusinessAutomation.Core.Abstractions.ExpressionEngine.Models;
using BusinessAutomation.Core.Abstractions.ExpressionEngine.Services;
using Domain.Entities;
using BusinessAutomation.Core.Abstractions.Events;
using BusinessAutomation.Core.Infrastructure.Events.Services;
using SharedKernel.Domain;
using System;
using System.Collections.Generic;

namespace Infrastructure.Events.Examples;

/// <summary>
/// Ukázkové vlastní definice událostí demonstrující pokročilé použití systému.
/// </summary>
public static class CustomEventDefinitions
{
    /// <summary>
    /// Vytvoří definici události pro významné změny věku (milníky).
    /// </summary>
    /// <returns>Definice události</returns>
    public static BusinessAutomation.Core.Abstractions.Events.Models.EventDefinition CreateAgeMilestoneEvent()
    {
        return new BusinessAutomation.Core.Abstractions.Events.Models.EventDefinition("SampleEntityAgeMilestone", typeof(SampleEntity), EventType.Updated)
            .WithTrackedProperties(nameof(SampleEntity.Age))
            .WithCondition("SampleEntityAgeMilestoneCheck")
            .WithDescription("Událost spuštěná při dosažení významného věkového milníku");
    }

    /// <summary>
    /// Vytvoří definici události pro změnu na prázdný nebo neprázdný popis.
    /// </summary>
    /// <returns>Definice události</returns>
    public static BusinessAutomation.Core.Abstractions.Events.Models.EventDefinition CreateDescriptionStatusEvent()
    {
        return new BusinessAutomation.Core.Abstractions.Events.Models.EventDefinition("SampleEntityDescriptionStatusChanged", typeof(SampleEntity), EventType.Updated)
            .WithTrackedProperties(nameof(SampleEntity.Description))
            .WithDescription("Událost spuštěná při změně stavu popisu (prázdný/neprázdný)");
    }

    /// <summary>
    /// Vytvoří definici události pro kompletní profil (všechny povinné údaje vyplněny).
    /// </summary>
    /// <returns>Definice události</returns>
    public static BusinessAutomation.Core.Abstractions.Events.Models.EventDefinition CreateCompleteProfileEvent()
    {
        return new BusinessAutomation.Core.Abstractions.Events.Models.EventDefinition("SampleEntityProfileCompleted", typeof(SampleEntity), EventType.Updated)
            .WithTrackedProperties(nameof(SampleEntity.Name), nameof(SampleEntity.Description),
                                 nameof(SampleEntity.Age), nameof(SampleEntity.DateOfBirth))
            .WithCondition("SampleEntityProfileCompletedCheck")
            .WithDescription("Událost spuštěná při dokončení kompletního profilu entity");
    }

    /// <summary>
    /// Vytvoří definici události pro nekonzistentní data (věk neodpovídá datu narození).
    /// </summary>
    /// <returns>Definice události</returns>
    public static BusinessAutomation.Core.Abstractions.Events.Models.EventDefinition CreateDataInconsistencyEvent()
    {
        return new BusinessAutomation.Core.Abstractions.Events.Models.EventDefinition("SampleEntityDataInconsistency", typeof(SampleEntity), EventType.Updated)
            .WithTrackedProperties(nameof(SampleEntity.Age), nameof(SampleEntity.DateOfBirth))
            .WithCondition("SampleEntityDataInconsistencyCheck")
            .WithDescription("Událost spuštěná při detekci nekonzistence mezi věkem a datem narození");
    }

    /// <summary>
    /// Vytvoří definici události pro dlouhý popis (více než určitý počet znaků).
    /// </summary>
    /// <param name="maxLength">Maximální délka popisu</param>
    /// <returns>Definice události</returns>
    public static BusinessAutomation.Core.Abstractions.Events.Models.EventDefinition CreateLongDescriptionEvent(int maxLength = 500)
    {
        return new BusinessAutomation.Core.Abstractions.Events.Models.EventDefinition("SampleEntityLongDescription", typeof(SampleEntity), EventType.Updated)
            .WithTrackedProperties(nameof(SampleEntity.Description))
            .WithCondition("SampleEntityLongDescriptionCheck")
            .WithDescription($"Událost spuštěná při nastavení popisu delšího než {maxLength} znaků");
    }

    /// <summary>
    /// Vytvoří definici události pro podezřelé změny (více vlastností změněno najednou).
    /// </summary>
    /// <returns>Definice události</returns>
    public static BusinessAutomation.Core.Abstractions.Events.Models.EventDefinition CreateSuspiciousChangesEvent()
    {
        return new BusinessAutomation.Core.Abstractions.Events.Models.EventDefinition("SampleEntitySuspiciousChanges", typeof(SampleEntity), EventType.Updated)
            .WithTrackedProperties(nameof(SampleEntity.Name), nameof(SampleEntity.Description), 
                                 nameof(SampleEntity.Age), nameof(SampleEntity.DateOfBirth))
            .WithDescription("Událost spuštěná při podezřelých změnách (více než 2 vlastnosti najednou)");
    }

    /// <summary>
    /// Získá všechny ukázkové definice událostí.
    /// </summary>
    /// <returns>Kolekce definic událostí</returns>
    public static IEnumerable<BusinessAutomation.Core.Abstractions.Events.Models.EventDefinition> GetAllCustomDefinitions()
    {
        return new[]
        {
            CreateAgeMilestoneEvent(),
            CreateDescriptionStatusEvent(),
            CreateCompleteProfileEvent(),
            CreateDataInconsistencyEvent(),
            CreateLongDescriptionEvent(),
            CreateSuspiciousChangesEvent()
        };
    }

    /// <summary>
    /// Uloží všechny ukázkové definice do databáze.
    /// </summary>
    /// <param name="eventDefinitionService">Služba pro správu definic událostí</param>
    /// <param name="cancellationToken">Token pro zrušení operace</param>
    public static async Task SaveAllCustomDefinitionsAsync(
        BusinessAutomation.Core.Infrastructure.Events.Services.EventDefinitionService eventDefinitionService,
        CancellationToken cancellationToken = default)
    {
        if (eventDefinitionService == null)
            throw new ArgumentNullException(nameof(eventDefinitionService));

        var definitions = GetAllCustomDefinitions().ToList();
        var savedCount = 0;

        foreach (var definition in definitions)
        {
            try
            {
                // Zkontrolujeme, zda definice již existuje
                var existing = await eventDefinitionService.GetByNameAsync(definition.EventName, cancellationToken);
                if (existing == null)
                {
                    await eventDefinitionService.CreateAsync(definition, cancellationToken);
                    savedCount++;
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Chyba při ukládání definice '{definition.EventName}': {ex.Message}");
            }
        }

        Console.WriteLine($"Uloženo {savedCount} z {definitions.Count} ukázkových definic událostí do databáze.");
    }




}
