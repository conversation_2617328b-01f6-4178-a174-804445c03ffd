using BusinessAutomation.Core.Abstractions.ExpressionEngine.Models;
using BusinessAutomation.Core.Abstractions.ExpressionEngine.Services;
using BusinessAutomation.Core.Abstractions.Database;
using Microsoft.EntityFrameworkCore;

namespace BusinessAutomation.Core.Infrastructure.ExpressionEngine.Services;

/// <summary>
/// Implementace IExpressionRepository pro správu výrazů v databázi.
/// Poskytuje CRUD operace pro Expression entity s podporou cachování.
/// </summary>
public class ExpressionRepository : IExpressionRepository
{
    private readonly IBusinessAutomationDbContext _context;
    private readonly IExpressionCacheService _cacheService;

    /// <summary>
    /// Inicializuje novou instanci ExpressionRepository.
    /// </summary>
    /// <param name="context">Databázový kontext</param>
    /// <param name="cacheService">Cache služba pro Expression Engine</param>
    public ExpressionRepository(IBusinessAutomationDbContext context, IExpressionCacheService cacheService)
    {
        _context = context;
        _cacheService = cacheService;
    }

    /// <summary>
    /// Získá všechny výrazy s cachováním.
    /// </summary>
    /// <returns>Kolekce všech výrazů</returns>
    public async Task<IEnumerable<BusinessAutomation.Core.Abstractions.ExpressionEngine.Models.Expression>> GetAllAsync()
    {
        const string cacheKey = "expressions:all";

        return await _cacheService.GetOrSetAsync(cacheKey, async () =>
        {
            return await _context.Set<BusinessAutomation.Core.Abstractions.ExpressionEngine.Models.Expression>()
                .Where(e => e.IsActive)
                .OrderBy(e => e.Name)
                .ToListAsync();
        }, TimeSpan.FromMinutes(15));
    }

    /// <summary>
    /// Získá výraz podle ID s cachováním.
    /// </summary>
    /// <param name="id">ID výrazu</param>
    /// <returns>Nalezený výraz nebo null</returns>
    public async Task<BusinessAutomation.Core.Abstractions.ExpressionEngine.Models.Expression?> GetByIdAsync(Guid id)
    {
        var cacheKey = $"expressions:id:{id}";

        return await _cacheService.GetOrSetAsync(cacheKey, async () =>
        {
            return await _context.Set<BusinessAutomation.Core.Abstractions.ExpressionEngine.Models.Expression>()
                .FirstOrDefaultAsync(e => e.Id == id);
        }, TimeSpan.FromMinutes(30));
    }

    /// <summary>
    /// Získá výraz podle názvu s cachováním.
    /// </summary>
    /// <param name="name">Název výrazu</param>
    /// <returns>Nalezený výraz nebo null</returns>
    public async Task<BusinessAutomation.Core.Abstractions.ExpressionEngine.Models.Expression?> GetByNameAsync(string name)
    {
        if (string.IsNullOrWhiteSpace(name))
            return null;

        var cacheKey = $"expressions:name:{name}";

        return await _cacheService.GetOrSetAsync(cacheKey, async () =>
        {
            return await _context.Set<BusinessAutomation.Core.Abstractions.ExpressionEngine.Models.Expression>()
                .FirstOrDefaultAsync(e => e.Name == name);
        }, TimeSpan.FromMinutes(30));
    }

    /// <summary>
    /// Přidá nový výraz a invaliduje související cache.
    /// </summary>
    /// <param name="expression">Výraz k přidání</param>
    public async Task AddAsync(BusinessAutomation.Core.Abstractions.ExpressionEngine.Models.Expression expression)
    {
        if (expression == null)
            throw new ArgumentNullException(nameof(expression));

        // Nastavíme ID pokud není nastaveno
        if (expression.Id == Guid.Empty)
            expression.Id = Guid.NewGuid();

        _context.Set<BusinessAutomation.Core.Abstractions.ExpressionEngine.Models.Expression>().Add(expression);
        await _context.SaveChangesAsync(CancellationToken.None);

        // Invalidujeme cache
        await InvalidateExpressionCacheAsync(expression);
    }

    /// <summary>
    /// Aktualizuje existující výraz a invaliduje související cache.
    /// </summary>
    /// <param name="expression">Výraz k aktualizaci</param>
    public async Task UpdateAsync(BusinessAutomation.Core.Abstractions.ExpressionEngine.Models.Expression expression)
    {
        if (expression == null)
            throw new ArgumentNullException(nameof(expression));

        var existingExpression = await _context.Set<BusinessAutomation.Core.Abstractions.ExpressionEngine.Models.Expression>()
            .FirstOrDefaultAsync(e => e.Id == expression.Id);

        if (existingExpression == null)
            throw new InvalidOperationException($"Výraz s ID {expression.Id} nebyl nalezen.");

        // Aktualizujeme vlastnosti
        existingExpression.Name = expression.Name;
        existingExpression.Description = expression.Description;
        existingExpression.SchemaVersion = expression.SchemaVersion;
        existingExpression.ExpressionTree = expression.ExpressionTree;
        existingExpression.IsActive = expression.IsActive;
        existingExpression.InternalNotes = expression.InternalNotes;
        existingExpression.Version = expression.Version;
        existingExpression.EffectiveFrom = expression.EffectiveFrom;
        existingExpression.EffectiveTo = expression.EffectiveTo;

        // UpdatedAt se nastavuje automaticky v TrackableEntityInterceptor
        await _context.SaveChangesAsync(CancellationToken.None);

        // Invalidujeme cache
        await InvalidateExpressionCacheAsync(existingExpression);
    }

    /// <summary>
    /// Smaže výraz podle ID a invaliduje související cache.
    /// </summary>
    /// <param name="id">ID výrazu ke smazání</param>
    public async Task DeleteAsync(Guid id)
    {
        var expression = await _context.Set<BusinessAutomation.Core.Abstractions.ExpressionEngine.Models.Expression>()
            .FirstOrDefaultAsync(e => e.Id == id);

        if (expression == null)
            throw new InvalidOperationException($"Výraz s ID {id} nebyl nalezen.");

        _context.Set<BusinessAutomation.Core.Abstractions.ExpressionEngine.Models.Expression>().Remove(expression);
        await _context.SaveChangesAsync(CancellationToken.None);

        // Invalidujeme cache
        await InvalidateExpressionCacheAsync(expression);
    }

    /// <summary>
    /// Ověří, zda existuje výraz se zadaným názvem (pro validaci duplicit).
    /// </summary>
    /// <param name="name">Název výrazu</param>
    /// <param name="excludeId">ID výrazu k vyloučení z kontroly (pro update)</param>
    /// <returns>True pokud výraz s názvem existuje</returns>
    public async Task<bool> ExistsWithNameAsync(string name, Guid? excludeId = null)
    {
        if (string.IsNullOrWhiteSpace(name))
            return false;

        var query = _context.Set<BusinessAutomation.Core.Abstractions.ExpressionEngine.Models.Expression>().Where(e => e.Name == name);

        if (excludeId.HasValue)
            query = query.Where(e => e.Id != excludeId.Value);

        return await query.AnyAsync();
    }

    /// <summary>
    /// Invaliduje cache záznamy související s výrazem.
    /// </summary>
    /// <param name="expression">Výraz pro invalidaci cache</param>
    private async Task InvalidateExpressionCacheAsync(BusinessAutomation.Core.Abstractions.ExpressionEngine.Models.Expression expression)
    {
        // Invalidujeme všechny cache záznamy pro tento výraz
        await _cacheService.InvalidateAsync($"*:{expression.Id}:*");

        // Invalidujeme obecné cache
        await _cacheService.InvalidateAsync("expressions:all");
    }
}
