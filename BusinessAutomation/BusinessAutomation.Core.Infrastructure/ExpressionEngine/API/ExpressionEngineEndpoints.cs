using System;
using System.Collections.Generic;
using System.Linq;
using System.Text.Json;
using Application.Abstraction;
using Domain.Entities;
using BusinessAutomation.Core.Abstractions.ExpressionEngine.Models;
using BusinessAutomation.Core.Abstractions.ExpressionEngine.Services;
using BusinessAutomation.Core.Infrastructure.Common.Services;
using BusinessAutomation.Core.Infrastructure.ExpressionEngine.Core;
using Microsoft.AspNetCore.Builder;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Routing;
using Microsoft.EntityFrameworkCore;

namespace BusinessAutomation.Core.Infrastructure.ExpressionEngine.API;

/// <summary>
/// Minimal API endpointy pro ExpressionEngine systém.
/// Poskytuje technické rozhraní pro vytváření, úpravu a testování výrazů.
/// </summary>
public static class ExpressionEngineEndpoints
{
    /// <summary>
    /// Registruje všechny ExpressionEngine endpointy.
    /// </summary>
    public static IEndpointRouteBuilder MapExpressionEngineEndpoints(this IEndpointRouteBuilder app)
    {
        var group = app.MapGroup("/api/expression-engine")
            .WithTags("ExpressionEngine");

        // GET /api/expression-engine/expressions - Získá všechny výrazy
        group.MapGet("/expressions", async (
            [FromServices] IExpressionRepository repository) =>
        {
            try
            {
                var expressions = await repository.GetAllAsync();
                return Results.Ok(expressions);
            }
            catch (Exception ex)
            {
                return Results.Problem($"Chyba při načítání výrazů: {ex.Message}");
            }
        })
        .WithName("GetAllExpressions")
        .WithSummary("Získá všechny výrazy")
        .WithDescription("Vrací seznam všech výrazů v systému")
        .Produces<IEnumerable<Expression>>(StatusCodes.Status200OK)
        .Produces(StatusCodes.Status500InternalServerError);

        // GET /api/expression-engine/expressions/{id} - Získá konkrétní výraz
        group.MapGet("/expressions/{id:guid}", async (
            Guid id,
            [FromServices] IExpressionRepository repository) =>
        {
            try
            {
                var expression = await repository.GetByIdAsync(id);
                if (expression == null)
                {
                    return Results.NotFound($"Výraz s ID {id} nebyl nalezen.");
                }
                return Results.Ok(expression);
            }
            catch (Exception ex)
            {
                return Results.Problem($"Chyba při načítání výrazu: {ex.Message}");
            }
        })
        .WithName("GetExpression")
        .WithSummary("Získá výraz podle ID")
        .WithDescription("Vrací konkrétní výraz včetně jeho struktury")
        .Produces<Expression>(StatusCodes.Status200OK)
        .Produces(StatusCodes.Status404NotFound)
        .Produces(StatusCodes.Status500InternalServerError);

        // POST /api/expression-engine/expressions - Vytvoří nové pravidlo
        group.MapPost("/expressions", async (
            [FromBody] Expression expression,
            [FromServices] IExpressionRepository repository) =>
        {
            try
            {
                // Kontrola duplicitního názvu
                if (await repository.ExistsWithNameAsync(expression.Name, null))
                {
                    return Results.BadRequest($"Pravidlo s názvem '{expression.Name}' již existuje.");
                }

                // Nastavení základních vlastností
                expression.Id = Guid.NewGuid();
                expression.SchemaVersion = "1.0";

                // TODO: Přidat validaci syntaxe po opravě CalculationEngine
                // var validationResult = await engine.ValidateExpressionAsync(expression);
                // if (!validationResult.IsValid)
                // {
                //     return Results.BadRequest(new {
                //         Message = "Výraz obsahuje syntaktické chyby.",
                //         Errors = new[] { validationResult.ErrorMessage }
                //     });
                // }

                await repository.AddAsync(expression);
                return Results.CreatedAtRoute("GetExpression", new { id = expression.Id }, expression);
            }
            catch (Exception ex)
            {
                return Results.Problem($"Chyba při vytváření pravidla: {ex.Message}");
            }
        })
        .WithName("CreateRule")
        .WithSummary("Vytvoří nové obchodní pravidlo")
        .WithDescription("Vytvoří nové obchodní pravidlo s validací syntaxe")
        .Accepts<Expression>("application/json")
        .Produces<Expression>(StatusCodes.Status201Created)
        .Produces(StatusCodes.Status400BadRequest)
        .Produces(StatusCodes.Status500InternalServerError);

        // PUT /api/expression-engine/expressions/{id} - Aktualizuje pravidlo
        group.MapPut("/expressions/{id:guid}", async (
            Guid id,
            [FromBody] Expression updatedRule,
            [FromServices] IExpressionRepository repository,
            [FromServices] CalculationEngine engine) =>
        {
            try
            {
                var existingRule = await repository.GetByIdAsync(id);
                if (existingRule == null)
                {
                    return Results.NotFound($"Pravidlo s ID {id} nebylo nalezeno.");
                }

                // Kontrola duplicitního názvu (kromě aktuálního pravidla)
                if (await repository.ExistsWithNameAsync(updatedRule.Name, id))
                {
                    return Results.BadRequest($"Pravidlo s názvem '{updatedRule.Name}' již existuje.");
                }

                // Aktualizace pravidla (zachování ID a RowVersion)
                existingRule.Name = updatedRule.Name;
                existingRule.Description = updatedRule.Description;
                existingRule.ExpressionTree = updatedRule.ExpressionTree;
                existingRule.IsActive = updatedRule.IsActive;
                existingRule.InternalNotes = updatedRule.InternalNotes;
                existingRule.SchemaVersion = updatedRule.SchemaVersion;
                existingRule.Version = updatedRule.Version;
                existingRule.EffectiveFrom = updatedRule.EffectiveFrom;
                existingRule.EffectiveTo = updatedRule.EffectiveTo;

                // TODO: Přidat validaci syntaxe po opravě CalculationEngine
                // var validationResult = await engine.ValidateExpressionAsync(existingExpression);
                // if (!validationResult.IsValid)
                // {
                //     return Results.BadRequest(new {
                //         Message = "Výraz obsahuje syntaktické chyby.",
                //         Errors = new[] { validationResult.ErrorMessage }
                //     });
                // }

                await repository.UpdateAsync(existingRule);

                // Vymazání z cache
                // await engine.InvalidateExpressionAsync(id);

                return Results.NoContent();
            }
            catch (Exception ex)
            {
                return Results.Problem($"Chyba při aktualizaci pravidla: {ex.Message}");
            }
        })
        .WithName("UpdateRule")
        .WithSummary("Aktualizuje obchodní pravidlo")
        .WithDescription("Aktualizuje existující obchodní pravidlo s validací syntaxe")
        .Accepts<Expression>("application/json")
        .Produces(StatusCodes.Status204NoContent)
        .Produces(StatusCodes.Status400BadRequest)
        .Produces(StatusCodes.Status404NotFound)
        .Produces(StatusCodes.Status500InternalServerError);

        // DELETE /api/expression-engine/expressions/{id} - Smaže pravidlo
        group.MapDelete("/expressions/{id:guid}", async (
            Guid id,
            [FromServices] IExpressionRepository repository,
            [FromServices] CalculationEngine engine) =>
        {
            try
            {
                var rule = await repository.GetByIdAsync(id);
                if (rule == null)
                {
                    return Results.NotFound($"Pravidlo s ID {id} nebylo nalezeno.");
                }

                await repository.DeleteAsync(id);
                
                // Vymazání z cache
                // await engine.InvalidateExpressionAsync(id);
                
                return Results.NoContent();
            }
            catch (Exception ex)
            {
                return Results.Problem($"Chyba při mazání pravidla: {ex.Message}");
            }
        })
        .WithName("DeleteExpression")
        .WithSummary("Smaže výraz")
        .WithDescription("Smaže existující výraz ze systému")
        .Produces(StatusCodes.Status204NoContent)
        .Produces(StatusCodes.Status404NotFound)
        .Produces(StatusCodes.Status500InternalServerError);

        // POST /api/expression-engine/expressions/{id}/test-with-entity - Testuje výraz s reálnou entitou
        group.MapPost("/expressions/{id:guid}/test-with-entity", async (
            Guid id,
            [FromBody] ExpressionTestRequest request,
            [FromServices] IExpressionRepository repository,
            [FromServices] CalculationEngine engine) =>
        {
            try
            {
                var expression = await repository.GetByIdAsync(id);
                if (expression == null)
                {
                    return Results.NotFound($"Výraz s ID {id} nebyl nalezen.");
                }

                // Deserializace entity dat podle typu
                object entity;
                try
                {
                    entity = request.EntityType.ToLowerInvariant() switch
                    {
                        "sampleentity" => JsonSerializer.Deserialize<SampleEntity>(request.EntityData, new JsonSerializerOptions { PropertyNameCaseInsensitive = true }),
                        _ => throw new ArgumentException($"Nepodporovaný typ entity: {request.EntityType}")
                    };

                    if (entity == null)
                    {
                        return Results.BadRequest("Nepodařilo se deserializovat data entity.");
                    }
                }
                catch (JsonException ex)
                {
                    return Results.BadRequest($"Chyba při deserializaci entity dat: {ex.Message}");
                }

                // Spuštění výrazu
                var startTime = DateTime.UtcNow;
                var result = await engine.ExecuteAsync(expression, entity);
                var executionTime = DateTime.UtcNow - startTime;

                return Results.Ok(new ExpressionTestResponse
                {
                    ExpressionId = expression.Id,
                    ExpressionName = expression.Name,
                    EntityType = request.EntityType,
                    Result = result,
                    ExecutionTimeMs = executionTime.TotalMilliseconds,
                    Success = true,
                    ErrorMessage = null
                });
            }
            catch (Exception ex)
            {
                return Results.Ok(new ExpressionTestResponse
                {
                    ExpressionId = id,
                    ExpressionName = null,
                    EntityType = request?.EntityType,
                    Result = null,
                    ExecutionTimeMs = 0,
                    Success = false,
                    ErrorMessage = ex.Message
                });
            }
        })
        .WithName("TestExpressionWithEntity")
        .WithSummary("Testuje výraz s reálnou entitou")
        .WithDescription("Spustí výraz nad poskytnutou entitou a vrátí výsledek s diagnostickými informacemi")
        .Accepts<ExpressionTestRequest>("application/json")
        .Produces<ExpressionTestResponse>(StatusCodes.Status200OK)
        .Produces(StatusCodes.Status400BadRequest)
        .Produces(StatusCodes.Status404NotFound);

        // GET /api/expression-engine/health - Jednoduchý health check endpoint
        group.MapGet("/health", () =>
        {
            return Results.Ok(new
            {
                Status = "Healthy",
                Timestamp = DateTime.UtcNow,
                Message = "ExpressionEngine API je funkční"
            });
        })
        .WithName("ExpressionEngineHealth")
        .WithSummary("Health check pro ExpressionEngine")
        .WithDescription("Jednoduchý endpoint pro ověření funkčnosti ExpressionEngine API")
        .Produces(StatusCodes.Status200OK);

        // GET /api/expression-engine/sample-entities - Získá ukázkové entity pro testování (mock data)
        group.MapGet("/sample-entities", ([FromQuery] int take = 5) =>
        {
            // Mock data pro testování
            var mockEntities = new[]
            {
                new
                {
                    Id = 1,
                    Name = "Jan Novák",
                    Description = "Testovací entita pro ExpressionEngine",
                    Age = 30,
                    DateOfBirth = new DateTime(1993, 5, 15),
                    IsActive = true,
                    InternalNotes = "Mock data pro testování"
                },
                new
                {
                    Id = 2,
                    Name = "Marie Svobodová",
                    Description = "Další testovací entita",
                    Age = 25,
                    DateOfBirth = new DateTime(1998, 8, 22),
                    IsActive = true,
                    InternalNotes = "Mock data pro testování"
                },
                new
                {
                    Id = 3,
                    Name = "Petr Dvořák",
                    Description = "Třetí testovací entita",
                    Age = 45,
                    DateOfBirth = new DateTime(1978, 12, 3),
                    IsActive = false,
                    InternalNotes = "Mock data pro testování"
                }
            };

            var selectedEntities = mockEntities.Take(Math.Min(take, mockEntities.Length));

            return Results.Ok(new
            {
                EntityType = "SampleEntity",
                Count = selectedEntities.Count(),
                Entities = selectedEntities,
                Note = "Toto jsou mock data pro testování. Pro reálná data z databáze použijte endpoint /sample-entities-db"
            });
        })
        .WithName("GetSampleEntities")
        .WithSummary("Získá ukázkové entity pro testování (mock data)")
        .WithDescription("Vrací mock seznam SampleEntity pro testování výrazů")
        .Produces(StatusCodes.Status200OK);

        // POST /api/expression-engine/test-simple - Jednoduchý test endpoint pro ověření funkčnosti
        group.MapPost("/test-simple", ([FromBody] SimpleExpressionTestRequest request) =>
        {
            try
            {
                // Jednoduchý test bez databáze - pouze aritmetické operace
                var result = request.Operation.ToLowerInvariant() switch
                {
                    "add" => request.Value1 + request.Value2,
                    "subtract" => request.Value1 - request.Value2,
                    "multiply" => request.Value1 * request.Value2,
                    "divide" => request.Value2 != 0 ? request.Value1 / request.Value2 : throw new DivideByZeroException(),
                    _ => throw new ArgumentException($"Nepodporovaná operace: {request.Operation}")
                };

                return Results.Ok(new SimpleExpressionTestResponse
                {
                    Operation = request.Operation,
                    Value1 = request.Value1,
                    Value2 = request.Value2,
                    Result = result,
                    Success = true,
                    Message = $"Operace {request.Operation}({request.Value1}, {request.Value2}) = {result}"
                });
            }
            catch (Exception ex)
            {
                return Results.Ok(new SimpleExpressionTestResponse
                {
                    Operation = request.Operation,
                    Value1 = request.Value1,
                    Value2 = request.Value2,
                    Result = 0,
                    Success = false,
                    Message = $"Chyba: {ex.Message}"
                });
            }
        })
        .WithName("TestSimpleExpression")
        .WithSummary("Jednoduchý test aritmetických operací")
        .WithDescription("Testuje základní aritmetické operace bez použití databáze")
        .Accepts<SimpleExpressionTestRequest>("application/json")
        .Produces<SimpleExpressionTestResponse>(StatusCodes.Status200OK);

        return app;
    }
}

/// <summary>
/// Request objekt pro jednoduchý test výrazu.
/// </summary>
public class SimpleExpressionTestRequest
{
    /// <summary>
    /// Operace k provedení (Add, Subtract, Multiply, Divide).
    /// </summary>
    public required string Operation { get; set; }

    /// <summary>
    /// První hodnota.
    /// </summary>
    public double Value1 { get; set; }

    /// <summary>
    /// Druhá hodnota.
    /// </summary>
    public double Value2 { get; set; }
}

/// <summary>
/// Response objekt s výsledkem jednoduchého testu.
/// </summary>
public class SimpleExpressionTestResponse
{
    /// <summary>
    /// Provedená operace.
    /// </summary>
    public string Operation { get; set; } = string.Empty;

    /// <summary>
    /// První hodnota.
    /// </summary>
    public double Value1 { get; set; }

    /// <summary>
    /// Druhá hodnota.
    /// </summary>
    public double Value2 { get; set; }

    /// <summary>
    /// Výsledek operace.
    /// </summary>
    public double Result { get; set; }

    /// <summary>
    /// Indikuje, zda byl test úspěšný.
    /// </summary>
    public bool Success { get; set; }

    /// <summary>
    /// Zpráva s detaily o výsledku.
    /// </summary>
    public string Message { get; set; } = string.Empty;
}

/// <summary>
/// Request objekt pro testování výrazu s entitou.
/// </summary>
public class ExpressionTestRequest
{
    /// <summary>
    /// Typ entity (např. "SampleEntity").
    /// </summary>
    public required string EntityType { get; set; }

    /// <summary>
    /// JSON data entity.
    /// </summary>
    public required JsonElement EntityData { get; set; }
}

/// <summary>
/// Response objekt s výsledkem testování výrazu.
/// </summary>
public class ExpressionTestResponse
{
    /// <summary>
    /// ID testovaného výrazu.
    /// </summary>
    public Guid ExpressionId { get; set; }

    /// <summary>
    /// Název testovaného výrazu.
    /// </summary>
    public string? ExpressionName { get; set; }

    /// <summary>
    /// Typ entity použité pro testování.
    /// </summary>
    public string? EntityType { get; set; }

    /// <summary>
    /// Výsledek vykonání výrazu.
    /// </summary>
    public object? Result { get; set; }

    /// <summary>
    /// Čas vykonání v milisekundách.
    /// </summary>
    public double ExecutionTimeMs { get; set; }

    /// <summary>
    /// Indikuje, zda bylo testování úspěšné.
    /// </summary>
    public bool Success { get; set; }

    /// <summary>
    /// Chybová zpráva v případě neúspěchu.
    /// </summary>
    public string? ErrorMessage { get; set; }
}
