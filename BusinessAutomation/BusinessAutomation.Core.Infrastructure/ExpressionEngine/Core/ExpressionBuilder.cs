using BusinessAutomation.Core.Abstractions.ExpressionEngine.Models;
using BusinessAutomation.Core.Abstractions.ExpressionEngine.Builders;
using BusinessAutomation.Core.Abstractions.ExpressionEngine.Services;
using System;
using System.Linq.Expressions;
using BusinessAutomation.Core.Abstractions;
using BusinessAutomation.Core.Abstractions.ExpressionEngine.Builders;

namespace BusinessAutomation.Core.Infrastructure.ExpressionEngine.Core;

/// <summary>
/// Kompozitní implementation of expression builder from RuleNode structure.
/// Deleguje práci na specializované buildery pro lepší udržovatelnost.
/// Compiles rules into Expression Trees for high performance.
/// </summary>
public class ExpressionBuilder : IExpressionBuilder
{
    private readonly IConstantExpressionBuilder _constantBuilder;
    private readonly ISourceExpressionBuilder _sourceBuilder;
    private readonly IOperationExpressionBuilder _operationBuilder;
    private readonly ILookupExpressionBuilder _lookupBuilder;
    private readonly IAggregationExpressionBuilder _aggregationBuilder;
    private readonly Action<ExpressionNode, System.Linq.Expressions.Expression>? _logHook;

    /// <summary>
    /// Inicializuje novou instanci ExpressionBuilder se specializovanými buildery.
    /// </summary>
    /// <param name="constantBuilder">Builder pro konstantní výrazy</param>
    /// <param name="sourceBuilder">Builder pro zdrojové výrazy</param>
    /// <param name="operationBuilder">Builder pro operační výrazy</param>
    /// <param name="lookupBuilder">Builder pro lookup výrazy</param>
    /// <param name="aggregationBuilder">Builder pro agregační výrazy</param>
    /// <param name="logHook">Volitelný hook pro logování</param>
    public ExpressionBuilder(
        IConstantExpressionBuilder constantBuilder,
        ISourceExpressionBuilder sourceBuilder,
        IOperationExpressionBuilder operationBuilder,
        ILookupExpressionBuilder lookupBuilder,
        IAggregationExpressionBuilder aggregationBuilder,
        Action<ExpressionNode, System.Linq.Expressions.Expression>? logHook = null)
    {
        _constantBuilder = constantBuilder ?? throw new ArgumentNullException(nameof(constantBuilder));
        _sourceBuilder = sourceBuilder ?? throw new ArgumentNullException(nameof(sourceBuilder));
        _operationBuilder = operationBuilder ?? throw new ArgumentNullException(nameof(operationBuilder));
        _lookupBuilder = lookupBuilder ?? throw new ArgumentNullException(nameof(lookupBuilder));
        _aggregationBuilder = aggregationBuilder ?? throw new ArgumentNullException(nameof(aggregationBuilder));
        _logHook = logHook;
    }

    /// <summary>
    /// Sestaví Expression z ExpressionNode struktury pomocí specializovaných builderů.
    /// </summary>
    /// <param name="node">Kořenový uzel výrazu</param>
    /// <param name="param">Parametr reprezentující vstupní entitu</param>
    /// <returns>Expression reprezentující logiku výrazu</returns>
    /// <exception cref="ArgumentNullException">Pokud node nebo param je null</exception>
    /// <exception cref="NotSupportedException">Pokud typ uzlu není podporován</exception>
    public System.Linq.Expressions.Expression Build(ExpressionNode node, ParameterExpression param)
    {
        if (node == null) throw new ArgumentNullException(nameof(node));
        if (param == null) throw new ArgumentNullException(nameof(param));

        System.Linq.Expressions.Expression expr = node switch
        {
            ConstantNode c => _constantBuilder.BuildConstant(c),
            SourceValueNode sv => _sourceBuilder.BuildSource(sv, param),
            OperationNode op => _operationBuilder.BuildOperation(op, param, Build),
            LookupNode lk => _lookupBuilder.BuildLookup(lk, param, Build),
            AggregationNode ag => _aggregationBuilder.BuildAggregation(ag, param, Build),
            RelatedAggregationNode ra => _aggregationBuilder.BuildRelatedAggregation(ra, param, Build),
            _ => throw new NotSupportedException($"Unsupported node type: {node.GetType().Name}")
        };

        _logHook?.Invoke(node, expr);
        return expr;
    }
}
