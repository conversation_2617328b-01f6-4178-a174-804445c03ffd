using BusinessAutomation.Core.Abstractions.ExpressionEngine.Models;
using BusinessAutomation.Core.Abstractions.ExpressionEngine.Builders;
using BusinessAutomation.Core.Abstractions.ExpressionEngine.Services;
using System.Linq.Expressions;
using BusinessAutomation.Core.Abstractions;
using BusinessAutomation.Core.Abstractions.Exceptions;
using BusinessAutomation.Core.Abstractions.Validation;
using BusinessAutomation.Core.Infrastructure.ExpressionEngine.Services;
using Microsoft.Extensions.Logging;

namespace BusinessAutomation.Core.Infrastructure.ExpressionEngine.Core;

/// <summary>
/// Zjednodušený engine pro vykonávání výrazů.
/// Zaměřený na jednoduchost a spolehlivost pro technické API.
/// Používá IExpressionCacheService pro pokročilé cachování.
/// </summary>
public class CalculationEngine
{
    private readonly IExpressionBuilder _builder;
    private readonly IReadOnlyDictionary<string, Type> _entityTypeMap;
    private readonly IExpressionCacheService _cacheService;
    private readonly ILogger<CalculationEngine> _logger;

    /// <summary>
    /// Konstruktor pro zpětnou kompatibilitu (bez cache služby).
    /// </summary>
    public CalculationEngine(
        IExpressionBuilder builder,
        IReadOnlyDictionary<string, Type> entityTypeMap,
        ILogger<CalculationEngine> logger)
        : this(builder, entityTypeMap, new ExpressionCacheService(
            new Microsoft.Extensions.Logging.Abstractions.NullLogger<ExpressionCacheService>()), logger)
    {
    }

    /// <summary>
    /// Hlavní konstruktor s cache službou.
    /// </summary>
    public CalculationEngine(
        IExpressionBuilder builder,
        IReadOnlyDictionary<string, Type> entityTypeMap,
        IExpressionCacheService cacheService,
        ILogger<CalculationEngine> logger)
    {
        _builder = builder ?? throw new ArgumentNullException(nameof(builder));
        _entityTypeMap = entityTypeMap ?? throw new ArgumentNullException(nameof(entityTypeMap));
        _cacheService = cacheService ?? throw new ArgumentNullException(nameof(cacheService));
        _logger = logger ?? throw new ArgumentNullException(nameof(logger));
    }

    /// <summary>
    /// Vykoná výraz na zadané entitě (synchronní verze pro zpětnou kompatibilitu).
    /// </summary>
    /// <param name="expression">Výraz k vykonání</param>
    /// <param name="entity">Entita pro vyhodnocení</param>
    /// <returns>Výsledek vyhodnocení výrazu</returns>
    /// <exception cref="ArgumentNullException">Pokud expression nebo entity je null</exception>
    /// <exception cref="ExpressionExecutionException">Pokud dojde k chybě při vykonávání výrazu</exception>
    public object Execute(BusinessAutomation.Core.Abstractions.ExpressionEngine.Models.Expression expression, object entity)
    {
        return ExecuteAsync(expression, entity).GetAwaiter().GetResult();
    }

    /// <summary>
    /// Vykoná výraz na zadané entitě (asynchronní verze).
    /// </summary>
    /// <param name="expression">Výraz k vykonání</param>
    /// <param name="entity">Entita pro vyhodnocení</param>
    /// <returns>Výsledek vyhodnocení výrazu</returns>
    /// <exception cref="ArgumentNullException">Pokud expression nebo entity je null</exception>
    /// <exception cref="ExpressionExecutionException">Pokud dojde k chybě při vykonávání výrazu</exception>
    public async Task<object> ExecuteAsync(BusinessAutomation.Core.Abstractions.ExpressionEngine.Models.Expression expression, object entity)
    {
        if (expression == null) throw new ArgumentNullException(nameof(expression));
        if (entity == null) throw new ArgumentNullException(nameof(entity));

        _logger.LogDebug("Spouštím vykonávání výrazu '{ExpressionName}' (ID: {ExpressionId}) na entitě typu {EntityType}",
            expression.Name, expression.Id, entity.GetType().Name);

        try
        {
            var startTime = DateTime.UtcNow;
            var compiledExpression = await GetOrCompileExpressionAsync(expression, entity.GetType());
            var result = compiledExpression(entity);
            var executionTime = DateTime.UtcNow - startTime;

            _logger.LogDebug("Výraz '{ExpressionName}' úspěšně vykonán za {ExecutionTime}ms. Výsledek: {Result}",
                expression.Name, executionTime.TotalMilliseconds, result);

            return result;
        }
        catch (Exception ex) when (!(ex is ArgumentNullException))
        {
            _logger.LogError(ex, "Chyba při vykonávání výrazu '{ExpressionName}' (ID: {ExpressionId}): {ErrorMessage}",
                expression.Name, expression.Id, ex.Message);

            throw new ExpressionExecutionException(
                $"Chyba při vykonávání výrazu '{expression.Name}' (ID: {expression.Id}): {ex.Message}",
                ex);
        }
    }

    /// <summary>
    /// Validuje syntaktickou správnost výrazu bez jeho vykonání (synchronní verze pro zpětnou kompatibilitu).
    /// </summary>
    /// <param name="expression">Výraz k validaci</param>
    /// <returns>Výsledek validace</returns>
    public ExpressionValidationResult ValidateExpression(BusinessAutomation.Core.Abstractions.ExpressionEngine.Models.Expression expression)
    {
        return ValidateExpressionAsync(expression).GetAwaiter().GetResult();
    }

    /// <summary>
    /// Validuje syntaktickou správnost výrazu bez jeho vykonání (asynchronní verze).
    /// Používá cache pro uložení výsledků validace.
    /// </summary>
    /// <param name="expression">Výraz k validaci</param>
    /// <returns>Výsledek validace</returns>
    public async Task<ExpressionValidationResult> ValidateExpressionAsync(BusinessAutomation.Core.Abstractions.ExpressionEngine.Models.Expression expression)
    {
        _logger.LogDebug("Validuji výraz '{ExpressionName}' (ID: {ExpressionId})",
            expression?.Name ?? "null", expression?.Id ?? Guid.Empty);

        if (expression == null)
        {
            _logger.LogWarning("Pokus o validaci null výrazu");
            return ExpressionValidationResult.Invalid("Výraz nesmí být null.");
        }

        if (expression.ExpressionTree == null)
        {
            _logger.LogWarning("Výraz '{ExpressionName}' nemá definovaný strom výrazu", expression.Name);
            return ExpressionValidationResult.Invalid("Výraz musí obsahovat strom výrazu.");
        }

        // Použijeme cache pro validaci - klíč obsahuje hash výrazu
        var validationCacheKey = $"validation:{expression.Id}:{GetExpressionHash(expression)}";

        return await _cacheService.GetOrSetAsync(validationCacheKey, () =>
        {
            try
            {
                // Pokus o kompilaci pro ověření syntaxe - použijeme object jako obecný typ
                var startTime = DateTime.UtcNow;
                CompileExpression(expression, typeof(object));
                var validationTime = DateTime.UtcNow - startTime;

                _logger.LogDebug("Výraz '{ExpressionName}' úspěšně validován za {ValidationTime}ms",
                    expression.Name, validationTime.TotalMilliseconds);

                return ExpressionValidationResult.Valid();
            }
            catch (Exception ex)
            {
                _logger.LogWarning(ex, "Syntaktická chyba ve výrazu '{ExpressionName}': {ErrorMessage}",
                    expression.Name, ex.Message);
                return ExpressionValidationResult.Invalid($"Syntaktická chyba ve výrazu: {ex.Message}");
            }
        }, TimeSpan.FromMinutes(30)); // Validace se cachuje na 30 minut
    }

    private async Task<Func<object, object>> GetOrCompileExpressionAsync(BusinessAutomation.Core.Abstractions.ExpressionEngine.Models.Expression expression, Type entityType)
    {
        var compilationCacheKey = $"compiled:{expression.Id}:{GetExpressionHash(expression)}";

        return await _cacheService.GetOrSetAsync(compilationCacheKey, () =>
        {
            _logger.LogDebug("Kompiluji výraz '{ExpressionName}' (ID: {ExpressionId}) - není v cache",
                expression.Name, expression.Id);

            var startTime = DateTime.UtcNow;
            var compiledExpression = CompileExpression(expression, entityType);
            var compilationTime = DateTime.UtcNow - startTime;

            _logger.LogDebug("Výraz '{ExpressionName}' zkompilován za {CompilationTime}ms a uložen do cache",
                expression.Name, compilationTime.TotalMilliseconds);

            return compiledExpression;
        }, TimeSpan.FromHours(1)); // Zkompilované výrazy se cachují na 1 hodinu
    }

    /// <summary>
    /// Invaliduje všechny cache záznamy pro konkrétní výraz (synchronní verze pro zpětnou kompatibilitu).
    /// Používá se při aktualizaci nebo smazání výrazu.
    /// </summary>
    /// <param name="expressionId">ID výrazu k invalidaci</param>
    public void InvalidateExpression(Guid expressionId)
    {
        InvalidateExpressionAsync(expressionId).GetAwaiter().GetResult();
    }

    /// <summary>
    /// Invaliduje všechny cache záznamy pro konkrétní výraz (asynchronní verze).
    /// Používá se při aktualizaci nebo smazání výrazu.
    /// </summary>
    /// <param name="expressionId">ID výrazu k invalidaci</param>
    public async Task InvalidateExpressionAsync(Guid expressionId)
    {
        var invalidatedCount = await _cacheService.InvalidateAsync($"*:{expressionId}:*");
        _logger.LogDebug("Invalidováno {Count} cache záznamů pro výraz s ID {ExpressionId}", invalidatedCount, expressionId);
    }

    /// <summary>
    /// Vymaže celou cache (synchronní verze pro zpětnou kompatibilitu).
    /// </summary>
    public void ClearCache()
    {
        ClearCacheAsync().GetAwaiter().GetResult();
    }

    /// <summary>
    /// Vymaže celou cache (asynchronní verze).
    /// </summary>
    public async Task ClearCacheAsync()
    {
        await _cacheService.ClearAsync();
        _logger.LogInformation("Cache Expression Engine byla kompletně vymazána");
    }

    private Func<object, object> CompileExpression(BusinessAutomation.Core.Abstractions.ExpressionEngine.Models.Expression expression, Type entityType)
    {
        var paramEntity = System.Linq.Expressions.Expression.Parameter(entityType, "entity");
        var body = _builder.Build(expression.ExpressionTree, paramEntity);
        var result = System.Linq.Expressions.Expression.Convert(body, typeof(object));

        // Vytvoříme wrapper lambda, která přijme object a převede ho na správný typ
        var objectParam = System.Linq.Expressions.Expression.Parameter(typeof(object), "obj");
        var convertedParam = System.Linq.Expressions.Expression.Convert(objectParam, entityType);
        var bodyWithConversion = System.Linq.Expressions.Expression.Invoke(
            System.Linq.Expressions.Expression.Lambda(result, paramEntity),
            convertedParam);

        var lambda = System.Linq.Expressions.Expression.Lambda<Func<object, object>>(bodyWithConversion, objectParam);
        return lambda.Compile();
    }

    /// <summary>
    /// Vytvoří hash výrazu pro cache klíče.
    /// Hash se mění při změně struktury výrazu.
    /// </summary>
    /// <param name="expression">Výraz pro hash</param>
    /// <returns>Hash string</returns>
    private string GetExpressionHash(BusinessAutomation.Core.Abstractions.ExpressionEngine.Models.Expression expression)
    {
        // Hash založený na RowVersion, SchemaVersion a ID výrazu
        var hashInput = $"{Convert.ToBase64String(expression.RowVersion)}:{expression.SchemaVersion}:{expression.Id}";
        return hashInput.GetHashCode().ToString("X");
    }

    /// <summary>
    /// Ověří, zda je entita podporována v expression engine.
    /// </summary>
    /// <param name="entityName">Název entity</param>
    /// <returns>True pokud je entita podporována</returns>
    public bool IsEntitySupported(string entityName)
    {
        return !string.IsNullOrWhiteSpace(entityName) && _entityTypeMap.ContainsKey(entityName);
    }

    /// <summary>
    /// Získá seznam všech podporovaných entit.
    /// </summary>
    /// <returns>Kolekce názvů podporovaných entit</returns>
    public IEnumerable<string> GetSupportedEntities()
    {
        return _entityTypeMap.Keys;
    }
}
