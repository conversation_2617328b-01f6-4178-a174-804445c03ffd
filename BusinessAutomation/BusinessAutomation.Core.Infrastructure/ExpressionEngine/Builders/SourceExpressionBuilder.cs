using System.Collections.Generic;
using BusinessAutomation.Core.Abstractions.ExpressionEngine.Models;
using BusinessAutomation.Core.Abstractions.ExpressionEngine.Builders;
using BusinessAutomation.Core.Abstractions.ExpressionEngine.Services;
using System.Linq.Expressions;
using System.Reflection;
using BusinessAutomation.Core.Abstractions;
using BusinessAutomation.Core.Abstractions.ExpressionEngine.Builders;

namespace BusinessAutomation.Core.Infrastructure.ExpressionEngine.Builders;

/// <summary>
/// Implementace builderu zdrojových výrazů.
/// Zodpovídá za vytváření Expression objektů pro přístup k vlastnostem zdrojové entity.
/// </summary>
public class SourceExpressionBuilder : ISourceExpressionBuilder
{
    private readonly IReadOnlyDictionary<string, PropertyInfo>? _propertyMetadata;

    /// <summary>
    /// Inicializuje novou instanci SourceExpressionBuilder.
    /// </summary>
    /// <param name="propertyMetadata">Volitelná metadata vlastností pro optimalizovaný přístup</param>
    public SourceExpressionBuilder(IReadOnlyDictionary<string, PropertyInfo>? propertyMetadata = null)
    {
        _propertyMetadata = propertyMetadata;
    }

    /// <summary>
    /// Sestaví Expression z uzlu zdrojové hodnoty.
    /// </summary>
    /// <param name="node">Uzel zdrojové hodnoty s cestou k vlastnosti</param>
    /// <param name="param">Parametr reprezentující vstupní entitu</param>
    /// <returns>Expression reprezentující přístup k vlastnosti entity</returns>
    /// <exception cref="ArgumentNullException">Pokud je node nebo param null</exception>
    public System.Linq.Expressions.Expression BuildSource(SourceValueNode node, ParameterExpression param)
    {
        if (node == null) throw new ArgumentNullException(nameof(node));
        if (param == null) throw new ArgumentNullException(nameof(param));

        System.Linq.Expressions.Expression expr = param;
        foreach (var part in node.SourcePath.Split('.'))
        {
            if (_propertyMetadata != null && _propertyMetadata.TryGetValue(part, out var prop))
                expr = System.Linq.Expressions.Expression.Property(expr, prop);
            else
                expr = System.Linq.Expressions.Expression.PropertyOrField(expr, part);
        }
        return expr;
    }
}
