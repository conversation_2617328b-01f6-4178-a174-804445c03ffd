using BusinessAutomation.Core.Abstractions.ExpressionEngine.Models;
using BusinessAutomation.Core.Abstractions.ExpressionEngine.Builders;
using BusinessAutomation.Core.Abstractions.ExpressionEngine.Services;
using System.Globalization;
using System.Linq.Expressions;
using BusinessAutomation.Core.Abstractions;
using BusinessAutomation.Core.Abstractions.ExpressionEngine.Builders;

namespace BusinessAutomation.Core.Infrastructure.ExpressionEngine.Builders;

/// <summary>
/// Implementace builderu konstantních výrazů.
/// Zodpovídá za konverzi konstantních hodnot na Expression objekty.
/// </summary>
public class ConstantExpressionBuilder : IConstantExpressionBuilder
{
    /// <summary>
    /// Sestaví Expression z konstantního uzlu.
    /// </summary>
    /// <param name="node">Konstantní uzel s hodnotou a datovým typem</param>
    /// <returns>Expression reprezentuj<PERSON><PERSON><PERSON> konstantní hodnotu</returns>
    /// <exception cref="ArgumentNullException">Pokud je node null</exception>
    public System.Linq.Expressions.Expression BuildConstant(ConstantNode node)
    {
        if (node == null) throw new ArgumentNullException(nameof(node));

        object value = node.DataType switch
        {
            BusinessAutomation.Core.Abstractions.ExpressionEngine.Models.ValueType.Integer => int.Parse(node.Value, CultureInfo.InvariantCulture),
            BusinessAutomation.Core.Abstractions.ExpressionEngine.Models.ValueType.Decimal => decimal.Parse(node.Value, CultureInfo.InvariantCulture),
            BusinessAutomation.Core.Abstractions.ExpressionEngine.Models.ValueType.Boolean => bool.Parse(node.Value),
            BusinessAutomation.Core.Abstractions.ExpressionEngine.Models.ValueType.DateTime => DateTime.Parse(node.Value, CultureInfo.InvariantCulture),
            BusinessAutomation.Core.Abstractions.ExpressionEngine.Models.ValueType.Guid => Guid.Parse(node.Value),
            _ => node.Value
        };
        
        return System.Linq.Expressions.Expression.Constant(value, value.GetType());
    }
}
