using System;
using System.Collections.Generic;
using BusinessAutomation.Core.Abstractions.ExpressionEngine.Models;
using BusinessAutomation.Core.Abstractions.ExpressionEngine.Builders;
using BusinessAutomation.Core.Abstractions.ExpressionEngine.Services;
using System.Linq.Expressions;
using BusinessAutomation.Core.Abstractions;
using BusinessAutomation.Core.Abstractions.ExpressionEngine.Builders;

namespace BusinessAutomation.Core.Infrastructure.ExpressionEngine.Builders;

/// <summary>
/// Implementace builderu lookup výrazů.
/// Zodpovídá za vytváření Expression objektů pro lookup operace přes IExpressionDataProvider.
/// </summary>
public class LookupExpressionBuilder : ILookupExpressionBuilder
{
    private readonly IExpressionDataProvider _dataProvider;
    private readonly IReadOnlyDictionary<string, Type> _entityTypeMap;

    /// <summary>
    /// Inicializuje novou instanci LookupExpressionBuilder.
    /// </summary>
    /// <param name="dataProvider">Poskytovatel dat pro lookup operace</param>
    /// <param name="entityTypeMap">Mapa názvů entit na jejich typy</param>
    public LookupExpressionBuilder(IExpressionDataProvider dataProvider, IReadOnlyDictionary<string, Type> entityTypeMap)
    {
        _dataProvider = dataProvider ?? throw new ArgumentNullException(nameof(dataProvider));
        _entityTypeMap = entityTypeMap ?? throw new ArgumentNullException(nameof(entityTypeMap));
    }

    /// <summary>
    /// Sestaví Expression z lookup uzlu.
    /// </summary>
    /// <param name="node">Lookup uzel s podmínkou a cílovou entitou</param>
    /// <param name="param">Parametr reprezentující vstupní entitu</param>
    /// <param name="buildChild">Funkce pro sestavení podřízených uzlů</param>
    /// <returns>Expression reprezentující lookup operaci</returns>
    /// <exception cref="ArgumentNullException">Pokud je některý z parametrů null</exception>
    /// <exception cref="InvalidOperationException">Pokud cílová entita není nalezena v mapě typů</exception>
    public System.Linq.Expressions.Expression BuildLookup(LookupNode node, ParameterExpression param, Func<ExpressionNode, ParameterExpression, System.Linq.Expressions.Expression> buildChild)
    {
        if (node == null) throw new ArgumentNullException(nameof(node));
        if (param == null) throw new ArgumentNullException(nameof(param));
        if (buildChild == null) throw new ArgumentNullException(nameof(buildChild));

        if (!_entityTypeMap.TryGetValue(node.TargetEntityName, out var entityType))
        {
            throw new InvalidOperationException($"Neznámý typ entity: {node.TargetEntityName}");
        }

        var lambdaParam = System.Linq.Expressions.Expression.Parameter(entityType, "e");
        var body = buildChild(node.Condition, lambdaParam);
        var predicate = System.Linq.Expressions.Expression.Lambda(body, lambdaParam);
        var providerExpr = System.Linq.Expressions.Expression.Constant(_dataProvider);

        var findSingleMethod = typeof(IExpressionDataProvider).GetMethod(nameof(IExpressionDataProvider.FindSingle));
        if (findSingleMethod == null)
        {
            throw new InvalidOperationException("FindSingle metoda nebyla nalezena na IExpressionDataProvider");
        }

        var call = System.Linq.Expressions.Expression.Call(
            providerExpr,
            findSingleMethod,
            System.Linq.Expressions.Expression.Constant(node.TargetEntityName),
            System.Linq.Expressions.Expression.Constant(predicate, typeof(System.Linq.Expressions.Expression))
        );
        
        var casted = System.Linq.Expressions.Expression.Convert(call, entityType);
        return System.Linq.Expressions.Expression.PropertyOrField(casted, node.ReturnFieldPath);
    }
}
