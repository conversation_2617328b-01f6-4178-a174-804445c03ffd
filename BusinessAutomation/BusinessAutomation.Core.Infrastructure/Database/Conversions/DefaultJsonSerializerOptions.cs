using System.Text.Encodings.Web;
using System.Text.Json;
using System.Text.Json.Serialization;
using System.Text.Unicode;

namespace BusinessAutomation.Core.Infrastructure.Database.Conversions;

/// <summary>
/// Třída poskytující standardní konfiguraci pro serializaci a deserializaci JSON v BusinessAutomation systému.
/// Definuje jednotné nastavení pro všechny JSON operace.
/// </summary>
public static class DefaultJsonSerializerOptions
{
    /// <summary>
    /// Standardní konfigurace pro JSON serializaci a deserializaci.
    /// </summary>
    /// <remarks>
    /// Konfigurace zahrnuje:
    /// - Použití camelCase pro názvy vlastností
    /// - Case-insensitive deserializaci
    /// - Podporu pro serializaci výčtových typů jako řetězců
    /// - Podporu pro základní latinku a čínské znaky v kódování
    /// - Speciální konvertor pro ExpressionNode polymorfní hierarchii
    /// </remarks>
    public static JsonSerializerOptions Options => new()
    {
        // Nastavení encoderu pro podporu základní latinky a čínských znaků
        Encoder = JavaScriptEncoder.Create(UnicodeRanges.BasicLatin, UnicodeRanges.CjkUnifiedIdeographs),
        // Použití PascalCase pro názvy vlastností (např. FirstName) - kompatibilní s existujícími daty
        PropertyNamingPolicy = null,
        // Ignorování velikosti písmen při deserializaci
        PropertyNameCaseInsensitive = true,
        // Přidání konvertoru pro výčtové typy a ExpressionNode
        Converters = {
            new JsonStringEnumConverter(),
            new ExpressionNodeJsonConverter()
        }
    };
}
