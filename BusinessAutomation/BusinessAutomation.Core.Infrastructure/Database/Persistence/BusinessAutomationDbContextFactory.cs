using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Design;
using Microsoft.Extensions.Configuration;

namespace BusinessAutomation.Core.Infrastructure.Database.Persistence;

/// <summary>
/// Factory pro vytváření BusinessAutomationDbContext během design-time operací (migrations).
/// </summary>
public class BusinessAutomationDbContextFactory : IDesignTimeDbContextFactory<BusinessAutomationDbContext>
{
    /// <summary>
    /// Vytvoří instanci BusinessAutomationDbContext pro design-time operace.
    /// </summary>
    /// <param name="args">Argumenty příkazové řádky</param>
    /// <returns>Nakonfigurovaný BusinessAutomationDbContext</returns>
    public BusinessAutomationDbContext CreateDbContext(string[] args)
    {
        // Načte konfiguraci z appsettings.json
        var configuration = new ConfigurationBuilder()
            .SetBasePath(Directory.GetCurrentDirectory())
            .AddJsonFile("appsettings.json", optional: false)
            .AddJsonFile("appsettings.Development.json", optional: true)
            .Build();

        var optionsBuilder = new DbContextOptionsBuilder<BusinessAutomationDbContext>();
        
        // Získá connection string z konfigurace
        var connectionString = configuration.GetConnectionString("BusinessAutomationDatabase") 
                              ?? configuration.GetConnectionString("DefaultConnection")
                              ?? "Data Source=./Data/businessautomation.db";

        optionsBuilder.UseSqlite(connectionString);

        return new BusinessAutomationDbContext(optionsBuilder.Options);
    }
}
