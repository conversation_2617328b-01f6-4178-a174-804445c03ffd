using BusinessAutomation.Core.Abstractions.ExpressionEngine.Models;
using BusinessAutomation.Core.Abstractions.Events.Models;
using BusinessAutomation.Core.Abstractions.Database;
using BusinessAutomation.Core.Infrastructure.Database.Configurations;
using Microsoft.EntityFrameworkCore;
using SharedKernel.Domain;

namespace BusinessAutomation.Core.Infrastructure.Database.Persistence;

/// <summary>
/// Databázový kontext pro BusinessAutomation systém.
/// Spravuje Expression a EventDefinition entity.
/// </summary>
public class BusinessAutomationDbContext : DbContext, IBusinessAutomationDbContext
{
    /// <summary>
    /// Inicializuje novou instanci BusinessAutomationDbContext.
    /// </summary>
    /// <param name="options">Možnosti konfigurace DbContext</param>
    public BusinessAutomationDbContext(DbContextOptions<BusinessAutomationDbContext> options) : base(options)
    {
    }

    /// <summary>
    /// DbSet pro Expression entity.
    /// </summary>
    public DbSet<Expression> Expressions { get; set; } = null!;

    /// <summary>
    /// DbSet pro EventDefinition entity.
    /// </summary>
    public DbSet<EventDefinition> EventDefinitions { get; set; } = null!;

    /// <summary>
    /// Konfiguruje model při vytváření.
    /// </summary>
    /// <param name="modelBuilder">Builder pro konfiguraci modelu</param>
    protected override void OnModelCreating(ModelBuilder modelBuilder)
    {
        base.OnModelCreating(modelBuilder);

        // Aplikuje všechny konfigurace z aktuálního assembly
        modelBuilder.ApplyConfigurationsFromAssembly(typeof(BusinessAutomationDbContext).Assembly);

        // Konfigurace pro BaseEntity<T> - ignoruje DomainEvents
        foreach (var entityType in modelBuilder.Model.GetEntityTypes())
        {
            var clrType = entityType.ClrType;
            
            // Zkontrolujeme, zda entita dědí z BaseEntity<T> a má DomainEvents
            if (IsAssignableFromGeneric(clrType, typeof(BaseEntity<>)))
            {
                // Ignorujeme DomainEvents vlastnost
                var domainEventsProperty = clrType.GetProperty("DomainEvents");
                if (domainEventsProperty != null)
                {
                    modelBuilder.Entity(clrType).Ignore("DomainEvents");
                }
            }
        }
    }

    /// <summary>
    /// Ověří, zda typ je přiřaditelný z generického typu.
    /// </summary>
    /// <param name="type">Typ k ověření</param>
    /// <param name="genericType">Generický typ</param>
    /// <returns>True pokud je typ přiřaditelný</returns>
    private static bool IsAssignableFromGeneric(Type type, Type genericType)
    {
        var current = type;
        while (current != null)
        {
            if (current.IsGenericType && current.GetGenericTypeDefinition() == genericType)
                return true;
            current = current.BaseType;
        }
        return false;
    }
}
