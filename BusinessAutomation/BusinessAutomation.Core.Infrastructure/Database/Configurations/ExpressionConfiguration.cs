using BusinessAutomation.Core.Abstractions.ExpressionEngine.Models;
using BusinessAutomation.Core.Infrastructure.Database.Conversions;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using System.Text.Json;

namespace BusinessAutomation.Core.Infrastructure.Database.Configurations;

/// <summary>
/// Konfigurace Entity Framework pro Expression entitu.
/// Definuje mapování na databázovou tabulku a serializaci ExpressionNode jako JSON.
/// </summary>
public class ExpressionConfiguration : IEntityTypeConfiguration<Expression>
{
    /// <summary>
    /// Konfiguruje mapování Expression entity.
    /// </summary>
    /// <param name="builder">Builder pro konfiguraci entity</param>
    public void Configure(EntityTypeBuilder<Expression> builder)
    {
        // Název tabulky
        builder.ToTable("Expressions");

        // Primary key
        builder.HasKey(e => e.Id);

        // Vlastnosti
        builder.Property(e => e.Name)
            .IsRequired()
            .HasMaxLength(200)
            .HasComment("Název výrazu");

        builder.Property(e => e.Description)
            .HasMaxLength(1000)
            .HasComment("Popis účelu a použití výrazu");

        builder.Property(e => e.SchemaVersion)
            .IsRequired()
            .HasMaxLength(20)
            .HasDefaultValue("1.0")
            .HasComment("Verze schématu výrazu pro kompatibilitu");

        builder.Property(e => e.IsActive)
            .IsRequired()
            .HasDefaultValue(true)
            .HasComment("Určuje, zda je výraz aktivní");

        builder.Property(e => e.InternalNotes)
            .HasMaxLength(2000)
            .HasComment("Interní poznámky pro vývojáře");

        // Vlastnosti pro verzování
        builder.Property(e => e.Version)
            .IsRequired()
            .HasDefaultValue(1)
            .HasComment("Číslo verze výrazu pro podporu verzování");

        builder.Property(e => e.EffectiveFrom)
            .IsRequired()
            .HasComment("Datum a čas, od kterého je tato verze výrazu platná");

        builder.Property(e => e.EffectiveTo)
            .HasComment("Datum a čas, do kterého je tato verze výrazu platná");

        // ExpressionTree jako JSON s podporou polymorfních typů
        builder.Property(e => e.ExpressionTree)
            .IsRequired()
            .HasConversion(
                v => JsonSerializer.Serialize(v, DefaultJsonSerializerOptions.Options),
                v => JsonSerializer.Deserialize<ExpressionNode>(v, DefaultJsonSerializerOptions.Options)!)
            .HasColumnType("TEXT")
            .HasComment("Strom výrazu serializovaný jako JSON");

        // Indexy pro výkon
        builder.HasIndex(e => e.Name)
            .HasDatabaseName("IX_Expressions_Name");

        builder.HasIndex(e => e.IsActive)
            .HasDatabaseName("IX_Expressions_IsActive");

        // Indexy pro verzování
        builder.HasIndex(e => e.Version)
            .HasDatabaseName("IX_Expressions_Version");

        builder.HasIndex(e => e.EffectiveFrom)
            .HasDatabaseName("IX_Expressions_EffectiveFrom");

        builder.HasIndex(e => e.EffectiveTo)
            .HasDatabaseName("IX_Expressions_EffectiveTo");

        builder.HasIndex(e => new { e.Name, e.Version })
            .IsUnique()
            .HasDatabaseName("IX_Expressions_Name_Version");

        // RowVersion pro optimistické zamykání - pro SQLite použijeme BLOB s výchozí hodnotou
        builder.Property(e => e.RowVersion)
            .IsRequired()
            .HasColumnType("BLOB")
            .HasDefaultValue(new byte[] { 0, 0, 0, 0, 0, 0, 0, 1 })
            .HasComment("Verze řádku pro optimistické zamykání");
    }
}
