using BusinessAutomation.Core.Abstractions.ExpressionEngine.Models;
using BusinessAutomation.Core.Abstractions.Events.Models;
using Microsoft.EntityFrameworkCore;

namespace BusinessAutomation.Core.Abstractions.Database;

/// <summary>
/// Rozhraní pro BusinessAutomation databázový kontext.
/// </summary>
public interface IBusinessAutomationDbContext
{
    /// <summary>
    /// DbSet pro Expression entity.
    /// </summary>
    DbSet<Expression> Expressions { get; }

    /// <summary>
    /// DbSet pro EventDefinition entity.
    /// </summary>
    DbSet<EventDefinition> EventDefinitions { get; }

    /// <summary>
    /// Uloží změny do databáze.
    /// </summary>
    /// <param name="cancellationToken">Token pro zrušení operace</param>
    /// <returns>Počet ovlivněných záznamů</returns>
    Task<int> SaveChangesAsync(CancellationToken cancellationToken = default);

    /// <summary>
    /// Uloží změny do databáze synchronně.
    /// </summary>
    /// <returns>Počet ovlivněných záznamů</returns>
    int SaveChanges();

    /// <summary>
    /// Získá DbSet pro daný typ entity.
    /// </summary>
    /// <typeparam name="TEntity">Typ entity</typeparam>
    /// <returns>DbSet pro daný typ entity</returns>
    DbSet<TEntity> Set<TEntity>() where TEntity : class;
}
