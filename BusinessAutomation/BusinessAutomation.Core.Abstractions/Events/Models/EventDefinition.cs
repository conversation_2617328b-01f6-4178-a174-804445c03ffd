using SharedKernel.Domain;

namespace BusinessAutomation.Core.Abstractions.Events.Models;

/// <summary>
/// Definice doménové události - technická třída specifikující kdy a jak se má událost generovat.
/// Tato třída není součástí obchodních entit, ale slouží pro konfiguraci systému událostí.
/// </summary>
public class EventDefinition : BaseEntity
{
    /// <summary>
    /// Jedinečný název události.
    /// </summary>
    public string EventName { get; set; } = string.Empty;

    /// <summary>
    /// Název typu entity, na které událost vzniká (pro databázi).
    /// </summary>
    public string EntityTypeName { get; set; } = string.Empty;

    /// <summary>
    /// Typ entity, na které událost vzniká (pro runtime použití).
    /// Tato vlastnost se nepersistuje do databáze.
    /// </summary>
    public Type EntityType
    {
        get => string.IsNullOrEmpty(EntityTypeName) ? typeof(object) : Type.GetType(EntityTypeName) ?? typeof(object);
        set => EntityTypeName = value.AssemblyQualifiedName ?? value.FullName ?? value.Name;
    }

    /// <summary>
    /// Název entity pro lepší čitelnost.
    /// </summary>
    public string EntityName => EntityType.Name;

    /// <summary>
    /// Operace, při které se událost spouští.
    /// </summary>
    public EventType Operation { get; set; }

    /// <summary>
    /// Seznam vlastností, které se sledují pro změny (serializováno jako JSON).
    /// Pokud je prázdný, sledují se všechny vlastnosti.
    /// </summary>
    public string TrackedPropertiesJson { get; set; } = "[]";

    /// <summary>
    /// Seznam vlastností, které se sledují pro změny (pro runtime použití).
    /// Tato vlastnost se nepersistuje do databáze.
    /// </summary>
    public List<string> TrackedProperties
    {
        get => System.Text.Json.JsonSerializer.Deserialize<List<string>>(TrackedPropertiesJson) ?? new List<string>();
        set => TrackedPropertiesJson = System.Text.Json.JsonSerializer.Serialize(value);
    }

    /// <summary>
    /// Název výrazu z Expression Engine pro podmínku spuštění události.
    /// Výraz musí vracet boolean hodnotu.
    /// </summary>
    public string? ConditionExpressionName { get; set; }

    /// <summary>
    /// Popis události pro dokumentační účely.
    /// </summary>
    public string? Description { get; set; }

    /// <summary>
    /// Indikuje, zda je definice aktivní.
    /// </summary>
    public bool IsActive { get; set; } = true;

    /// <summary>
    /// Datum vytvoření definice.
    /// </summary>
    public DateTimeOffset CreatedAt { get; set; } = DateTimeOffset.UtcNow;

    /// <summary>
    /// Bezparametrový konstruktor pro Entity Framework.
    /// </summary>
    public EventDefinition()
    {
        RowVersion = new byte[] { 0, 0, 0, 0, 0, 0, 0, 1 };
    }

    /// <summary>
    /// Konstruktor pro vytvoření definice události.
    /// </summary>
    /// <param name="eventName">Název události</param>
    /// <param name="entityType">Typ entity</param>
    /// <param name="operation">Operace</param>
    public EventDefinition(string eventName, Type entityType, EventType operation)
    {
        EventName = eventName;
        EntityType = entityType;
        Operation = operation;
        RowVersion = new byte[] { 0, 0, 0, 0, 0, 0, 0, 1 };
    }

    /// <summary>
    /// Konstruktor pro vytvoření definice události s popisem.
    /// </summary>
    /// <param name="eventName">Název události</param>
    /// <param name="entityType">Typ entity</param>
    /// <param name="operation">Operace</param>
    /// <param name="description">Popis události</param>
    public EventDefinition(string eventName, Type entityType, EventType operation, string description)
        : this(eventName, entityType, operation)
    {
        Description = description;
    }

    /// <summary>
    /// Nastaví sledované vlastnosti.
    /// </summary>
    /// <param name="properties">Seznam názvů vlastností</param>
    /// <returns>Aktuální instanci pro fluent API</returns>
    public EventDefinition WithTrackedProperties(params string[] properties)
    {
        TrackedProperties.AddRange(properties);
        return this;
    }

    /// <summary>
    /// Nastaví podmínku pro spuštění události pomocí názvu výrazu z Expression Engine.
    /// </summary>
    /// <param name="conditionExpressionName">Název výrazu pro podmínku</param>
    /// <returns>Aktuální instanci pro fluent API</returns>
    public EventDefinition WithCondition(string conditionExpressionName)
    {
        ConditionExpressionName = conditionExpressionName;
        return this;
    }

    /// <summary>
    /// Nastaví popis události.
    /// </summary>
    /// <param name="description">Popis</param>
    /// <returns>Aktuální instanci pro fluent API</returns>
    public EventDefinition WithDescription(string description)
    {
        Description = description;
        return this;
    }

    /// <summary>
    /// Deaktivuje definici události.
    /// </summary>
    /// <returns>Aktuální instanci pro fluent API</returns>
    public EventDefinition Deactivate()
    {
        IsActive = false;
        return this;
    }

    /// <summary>
    /// Aktivuje definici události.
    /// </summary>
    /// <returns>Aktuální instanci pro fluent API</returns>
    public EventDefinition Activate()
    {
        IsActive = true;
        return this;
    }
}
