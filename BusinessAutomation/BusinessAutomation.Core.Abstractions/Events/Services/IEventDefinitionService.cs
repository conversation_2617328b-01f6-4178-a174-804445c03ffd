using BusinessAutomation.Core.Abstractions.Events.Models;
namespace BusinessAutomation.Core.Abstractions.Events.Services;

/// <summary>
/// Rozhraní pro službu správy definic událostí.
/// </summary>
public interface IEventDefinitionService
{
    /// <summary>
    /// Získá všechny definice událostí.
    /// </summary>
    Task<IEnumerable<EventDefinition>> GetAllAsync();

    /// <summary>
    /// Z<PERSON>k<PERSON> definici události podle ID.
    /// </summary>
    Task<EventDefinition?> GetByIdAsync(int id);

    /// <summary>
    /// Přidá novou definici události.
    /// </summary>
    Task AddAsync(EventDefinition definition);

    /// <summary>
    /// Aktualizuje definici události.
    /// </summary>
    Task UpdateAsync(EventDefinition definition);

    /// <summary>
    /// <PERSON><PERSON><PERSON><PERSON> definici události.
    /// </summary>
    Task DeleteAsync(int id);
}
