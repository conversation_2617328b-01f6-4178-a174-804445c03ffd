using System.ComponentModel.DataAnnotations;

namespace BusinessAutomation.Core.Abstractions.ExpressionEngine.Models;

/// <summary>
/// Technická entita reprezentující výraz pro výpočty a validace.
/// Obsahuje definici výrazu ve formě stromu uzlů (ExpressionNode).
/// Výraz sám o sobě pouze vrací hodnotu a neví, kde se používá.
/// Jedná se o ryze technickou entitu bez trackování změn.
/// </summary>
public class Expression
{
    /// <summary>
    /// Jedinečný identifikátor výrazu.
    /// </summary>
    [Key]
    public Guid Id { get; set; }

    /// <summary>
    /// Verze řádku pro optimistické zamykání.
    /// </summary>
    [Timestamp]
    public byte[] RowVersion { get; set; } = Array.Empty<byte>();

    /// <summary>
    /// Název výrazu pro identifikaci uživatelem.
    /// </summary>
    public required string Name { get; set; }

    /// <summary>
    /// Popis výrazu vysvětlují<PERSON><PERSON> jeho účel a použití.
    /// </summary>
    public string? Description { get; set; }

    /// <summary>
    /// Verze schématu výrazu pro zajištění kompatibility při změnách.
    /// </summary>
    public string SchemaVersion { get; set; } = "1.0";

    /// <summary>
    /// Strom výrazu obsahující logiku výpočtu.
    /// Obsahuje hierarchickou strukturu uzlů definující výraz.
    /// </summary>
    public required ExpressionNode ExpressionTree { get; set; }

    /// <summary>
    /// Určuje, zda je výraz aktivní a měl by být vykonáván.
    /// </summary>
    public bool IsActive { get; set; } = true;

    /// <summary>
    /// Interní poznámky pro vývojáře.
    /// </summary>
    public string? InternalNotes { get; set; }

    /// <summary>
    /// Číslo verze výrazu pro podporu verzování.
    /// </summary>
    public int Version { get; set; } = 1;

    /// <summary>
    /// Datum a čas, od kterého je tato verze výrazu platná.
    /// </summary>
    public DateTime EffectiveFrom { get; set; } = DateTime.UtcNow;

    /// <summary>
    /// Datum a čas, do kterého je tato verze výrazu platná.
    /// Null znamená, že verze je stále aktivní.
    /// </summary>
    public DateTime? EffectiveTo { get; set; }
}
