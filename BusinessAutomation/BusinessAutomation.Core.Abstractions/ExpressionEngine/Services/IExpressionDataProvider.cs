using BusinessAutomation.Core.Abstractions.ExpressionEngine.Models;
using System.Linq.Expressions;

namespace BusinessAutomation.Core.Abstractions.ExpressionEngine.Services;

public interface IExpressionDataProvider
{
    /// <summary>
    /// Najde jediný záznam podle Expression filtru nebo vyhodí NotFoundException.
    /// </summary>
    object FindSingle(string entityName, System.Linq.Expressions.Expression filterExpression);

    /// <summary>
    /// Najde více záznamů podle Expression filtru.
    /// </summary>
    IEnumerable<object> FindMany(string entityName, System.Linq.Expressions.Expression filterExpression);
}
