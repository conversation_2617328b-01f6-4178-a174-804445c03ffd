using BusinessAutomation.Core.Abstractions.ExpressionEngine.Models;
namespace BusinessAutomation.Core.Abstractions.ExpressionEngine.Services;

/// <summary>
/// Rozhraní pro repository výrazů.
/// </summary>
public interface IExpressionRepository
{
    /// <summary>
    /// Získá všechny výrazy.
    /// </summary>
    Task<IEnumerable<Expression>> GetAllAsync();

    /// <summary>
    /// Získá výraz podle ID.
    /// </summary>
    Task<Expression?> GetByIdAsync(Guid id);

    /// <summary>
    /// Získá výraz podle názvu.
    /// </summary>
    Task<Expression?> GetByNameAsync(string name);

    /// <summary>
    /// Přidá nový výraz.
    /// </summary>
    Task AddAsync(Expression expression);

    /// <summary>
    /// Aktualizuje existující výraz.
    /// </summary>
    Task UpdateAsync(Expression expression);

    /// <summary>
    /// <PERSON><PERSON><PERSON><PERSON> výraz podle ID.
    /// </summary>
    Task DeleteAsync(Guid id);

    /// <summary>
    /// Ověř<PERSON>, zda existuje výraz s daným názvem.
    /// </summary>
    Task<bool> ExistsWithNameAsync(string name, Guid? excludeId = null);
}
