using BusinessAutomation.Core.Abstractions.ExpressionEngine.Models;
using System.Linq.Expressions;

namespace BusinessAutomation.Core.Abstractions.ExpressionEngine.Builders;

/// <summary>
/// Rozhraní pro builder lookup výrazů.
/// Zodpovídá za vytváření Expression objektů pro lookup operace přes IExpressionDataProvider.
/// </summary>
public interface ILookupExpressionBuilder
{
    /// <summary>
    /// Sestaví Expression z lookup uzlu.
    /// </summary>
    /// <param name="node">Lookup uzel s podmínkou a cílovou entitou</param>
    /// <param name="param">Parametr reprezentují<PERSON><PERSON> vstupní entitu</param>
    /// <param name="buildChild">Funkce pro sestavení podřízených uzlů</param>
    /// <returns>Expression reprezentující lookup operaci</returns>
    System.Linq.Expressions.Expression BuildLookup(LookupNode node, ParameterExpression param, Func<ExpressionNode, ParameterExpression, System.Linq.Expressions.Expression> buildChild);
}
