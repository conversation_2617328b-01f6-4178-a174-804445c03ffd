using BusinessAutomation.Core.Abstractions.ExpressionEngine.Models;
using System.Linq.Expressions;

namespace BusinessAutomation.Core.Abstractions.ExpressionEngine.Builders;

/// <summary>
/// Rozhraní pro builder konstantních výrazů.
/// Zodpovídá za vytváření Expression objektů z konstantních hodnot.
/// </summary>
public interface IConstantExpressionBuilder
{
    /// <summary>
    /// Sestaví Expression z konstantního uzlu.
    /// </summary>
    /// <param name="node">Konstantní uzel s hodnotou a datovým typem</param>
    /// <returns>Expression reprezentující konstantní hodnotu</returns>
    System.Linq.Expressions.Expression BuildConstant(ConstantNode node);
}
