using BusinessAutomation.Core.Abstractions.ExpressionEngine.Models;
using System.Linq.Expressions;

namespace BusinessAutomation.Core.Abstractions.ExpressionEngine.Builders;

/// <summary>
/// Rozhraní pro builder operačních výrazů.
/// Zodpovídá za vytváření Expression objektů pro aritmetické, logické a podmíněné operace.
/// </summary>
public interface IOperationExpressionBuilder
{
    /// <summary>
    /// Sestaví Expression z operačního uzlu.
    /// </summary>
    /// <param name="node">Operační uzel s operátorem a operandy</param>
    /// <param name="param">Parametr reprezentujíc<PERSON> vstupní entitu</param>
    /// <param name="buildChild">Funkce pro sestavení podřízených uzlů</param>
    /// <returns>Expression reprezentující operaci</returns>
    System.Linq.Expressions.Expression BuildOperation(OperationNode node, ParameterExpression param, Func<ExpressionNode, ParameterExpression, System.Linq.Expressions.Expression> buildChild);
}
