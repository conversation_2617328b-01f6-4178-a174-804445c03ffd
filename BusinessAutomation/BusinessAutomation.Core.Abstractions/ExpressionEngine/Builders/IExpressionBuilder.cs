using BusinessAutomation.Core.Abstractions.ExpressionEngine.Models;
using System.Linq.Expressions;
using BusinessAutomation.Core.Abstractions.ExpressionEngine.Models;

namespace BusinessAutomation.Core.Abstractions.ExpressionEngine.Builders;

/// <summary>
/// Interface pro builder výrazů z ExpressionNode struktury.
/// Abstrakce umožňuje testování a různé implementace builderu.
/// </summary>
public interface IExpressionBuilder
{
    /// <summary>
    /// Sestaví Expression z ExpressionNode struktury.
    /// </summary>
    /// <param name="node">Kořenový uzel výrazu</param>
    /// <param name="param">Parametr reprezentující vstupní entitu</param>
    /// <returns>Expression reprezentující logiku výrazu</returns>
    System.Linq.Expressions.Expression Build(ExpressionNode node, ParameterExpression param);
}
