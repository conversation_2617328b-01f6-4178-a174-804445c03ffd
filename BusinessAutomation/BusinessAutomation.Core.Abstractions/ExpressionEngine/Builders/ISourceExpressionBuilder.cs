using BusinessAutomation.Core.Abstractions.ExpressionEngine.Models;
using System.Linq.Expressions;
using System.Reflection;

namespace BusinessAutomation.Core.Abstractions.ExpressionEngine.Builders;

/// <summary>
/// Rozhraní pro builder zdrojových výrazů.
/// Zodpovídá za vytváření Expression objektů pro přístup k vlastnostem zdrojové entity.
/// </summary>
public interface ISourceExpressionBuilder
{
    /// <summary>
    /// Sestaví Expression z uzlu zdrojové hodnoty.
    /// </summary>
    /// <param name="node">Uzel zdrojové hodnoty s cestou k vlastnosti</param>
    /// <param name="param">Parametr reprezentující vstupní entitu</param>
    /// <returns>Expression reprezentující přístup k vlastnosti entity</returns>
    System.Linq.Expressions.Expression BuildSource(SourceValueNode node, ParameterExpression param);
}
