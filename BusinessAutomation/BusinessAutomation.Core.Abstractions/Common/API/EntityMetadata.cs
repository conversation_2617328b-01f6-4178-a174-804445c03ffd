namespace BusinessAutomation.Core.Abstractions.Common.API;

/// <summary>
/// Metadata entity pro popis struktury entit v systému.
/// Používá se pro generování UI a validaci v Expression Engine.
/// </summary>
public class EntityMetadata
{
    /// <summary>
    /// Název entity.
    /// </summary>
    public required string Name { get; set; }

    /// <summary>
    /// Zobrazovaný název entity pro uživatele.
    /// </summary>
    public required string DisplayName { get; set; }

    /// <summary>
    /// Popis entity.
    /// </summary>
    public string? Description { get; set; }

    /// <summary>
    /// Seznam vlastností entity.
    /// </summary>
    public List<PropertyMetadata> Properties { get; set; } = new();

    /// <summary>
    /// Určuje, zda je entita dostupná pro použití ve výrazech.
    /// </summary>
    public bool IsAvailable { get; set; } = true;
}

/// <summary>
/// Metadata vlastnosti entity.
/// </summary>
public class PropertyMetadata
{
    /// <summary>
    /// Název vlastnosti.
    /// </summary>
    public required string Name { get; set; }

    /// <summary>
    /// Zobrazovaný název vlastnosti pro uživatele.
    /// </summary>
    public required string DisplayName { get; set; }

    /// <summary>
    /// Datový typ vlastnosti.
    /// </summary>
    public required string DataType { get; set; }

    /// <summary>
    /// Popis vlastnosti.
    /// </summary>
    public string? Description { get; set; }

    /// <summary>
    /// Určuje, zda je vlastnost povinná.
    /// </summary>
    public bool IsRequired { get; set; }

    /// <summary>
    /// Určuje, zda je vlastnost dostupná pro použití ve výrazech.
    /// </summary>
    public bool IsAvailable { get; set; } = true;

    /// <summary>
    /// Určuje, zda je vlastnost kolekce.
    /// </summary>
    public bool IsCollection { get; set; }

    /// <summary>
    /// Typ prvků kolekce (pokud je IsCollection true).
    /// </summary>
    public string? CollectionElementType { get; set; }

    /// <summary>
    /// Možné hodnoty pro výčtové typy.
    /// </summary>
    public List<string>? PossibleValues { get; set; }
}
