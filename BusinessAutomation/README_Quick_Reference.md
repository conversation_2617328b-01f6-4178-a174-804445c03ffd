# BusinessAutomation - Rychlý Referenční Průvodce

## 🚀 Rychlé Rozhodnutí: Kterou Událost Použít?

### ❓ Ptejte se:

1. **Je to kritický byznys proces?** → DDD Událost
2. **Potřebuje to silnou typovou kontrolu?** → DDD Událost  
3. **Bude se to často měnit?** → Dynamická Událost
4. **<PERSON><PERSON> to konfigurovat administrátor?** → Dynamická Událost
5. **Je to integrace s externím systémem?** → Dynamická Událost

## 📋 Checklist pro DDD Události

### ✅ Použijte DDD Události Když:
- [ ] Proces je **kritický** pro fungování aplikace
- [ ] Proces se **nemění** v čase
- [ ] Potřebujete **silnou typovou kontrolu**
- [ ] Je to **byznys logika** v doméně
- [ ] Chcete **unit testy** s mock objekty
- [ ] Proces **nesmí selhat** bez rollback

### 🔧 Implementace DDD Události:
```csharp
// 1. Definice události
public class OrderPaidEvent : IDomainEvent
{
    public Guid OrderId { get; }
    public decimal Amount { get; }
    // konstruktor...
}

// 2. Vyvolání z AggregateRoot
public void MarkAsPaid(decimal amount)
{
    Status = OrderStatus.Paid;
    AddDomainEvent(new OrderPaidEvent(Id, amount));
}

// 3. Handler
public class OrderPaidEventHandler : INotificationHandler<OrderPaidEvent>
{
    public async Task Handle(OrderPaidEvent evt, CancellationToken ct)
    {
        // Kritická byznys logika
    }
}
```

## 📋 Checklist pro Dynamické Události

### ✅ Použijte Dynamické Události Když:
- [ ] Proces je **konfigurovatelný** uživatelem
- [ ] Požadavky se **často mění**
- [ ] Je to **notifikace** nebo **alert**
- [ ] Je to **integrace** s externím systémem
- [ ] Je to **webhook** nebo **callback**
- [ ] Chcete **runtime konfiguraci**

### 🔧 Konfigurace Dynamické Události:
```csharp
// 1. Definice v databázi
var eventDef = new EventDefinition
{
    EventName = "OrderStatusChanged",
    EntityType = typeof(Order),
    Operation = EventType.Update,
    TrackedProperties = new[] { "Status" },
    ConditionExpressionName = "StatusChangeCondition"
};

// 2. Automatické vyvolání interceptorem
// (žádný kód potřeba)

// 3. Generický handler
public class DynamicEventHandler : INotificationHandler<EntityChangedEvent>
{
    public async Task Handle(EntityChangedEvent evt, CancellationToken ct)
    {
        // Konfigurovatelná logika
    }
}
```

## 🎯 Konkrétní Příklady

### DDD Události - Příklady
```csharp
// ✅ SPRÁVNĚ - Kritické byznys procesy
order.MarkAsPaid(amount);           // → OrderPaidEvent
order.Cancel(reason);               // → OrderCancelledEvent  
user.Register(email, password);     // → UserRegisteredEvent
delivery.Complete();                // → DeliveryCompletedEvent
invoice.Generate();                 // → InvoiceGeneratedEvent
```

### Dynamické Události - Příklady
```json
// ✅ SPRÁVNĚ - Konfigurovatelné procesy
{
  "EventName": "LowStockAlert",
  "EntityType": "Product", 
  "TrackedProperties": ["StockQuantity"],
  "ConditionExpression": "StockQuantity < 10"
}

{
  "EventName": "CustomerDataSync",
  "EntityType": "Customer",
  "TrackedProperties": ["Email", "Phone"],
  "Description": "Sync s CRM systémem"
}

{
  "EventName": "OrderStatusWebhook", 
  "EntityType": "Order",
  "TrackedProperties": ["Status"],
  "Description": "Webhook pro e-shop"
}
```

## ❌ Časté Chyby

### Nesprávné Použití DDD Událostí
```csharp
// ❌ ŠPATNĚ - Konfigurovatelná logika v DDD události
public void UpdateEmail(string email)
{
    Email = email;
    // ŠPATNĚ - toto by měla být dynamická událost
    AddDomainEvent(new EmailChangedEvent(Id, email));
}

// ❌ ŠPATNĚ - Notifikace jako DDD událost
public void ChangeStatus(OrderStatus status)
{
    Status = status;
    // ŠPATNĚ - notifikace by měla být dynamická
    AddDomainEvent(new StatusChangedNotificationEvent(Id, status));
}
```

### Nesprávné Spoléhání na Dynamické Události
```csharp
// ❌ ŠPATNĚ - Kritický proces jako dynamická událost
// Platba by NIKDY neměla být pouze dynamická událost!
// Musí být DDD událost s možným doplněním dynamickou

public void ProcessPayment(decimal amount)
{
    Status = OrderStatus.Paid;
    // ŠPATNĚ - spoléhání pouze na dynamickou událost
    // pro kritický proces
}
```

## 🔍 Debugging Tips

### DDD Události
```csharp
// Logování v handleru
_logger.LogInformation("Zpracování {EventType} pro {EntityId}", 
    typeof(OrderPaidEvent).Name, evt.OrderId);

// Unit test
[Fact]
public void MarkAsPaid_ShouldRaiseOrderPaidEvent()
{
    // Arrange
    var order = new Order();
    
    // Act  
    order.MarkAsPaid(100m);
    
    // Assert
    var domainEvent = order.DomainEvents.OfType<OrderPaidEvent>().Single();
    Assert.Equal(100m, domainEvent.Amount);
}
```

### Dynamické Události
```csharp
// Logování v interceptoru
_logger.LogDebug("Detekována změna {EntityType}:{EntityId}, vlastnosti: {Properties}",
    entityType, entityId, string.Join(", ", changedProperties));

// Admin UI pro debugging
// - Seznam aktivních EventDefinition
// - Historie vyvolaných událostí  
// - Webhook delivery status
```

## 📊 Performance Tips

### DDD Události
- ✅ Rychlé - kompilované, silně typované
- ✅ Cacheable - stejné typy událostí
- ⚠️ Pozor na počet handlerů na jednu událost

### Dynamické Události  
- ⚠️ Pomalejší - runtime vyhodnocování
- ✅ Škálovatelné - jeden handler pro všechny
- ⚠️ Pozor na složité Expression podmínky

## 🎯 Rozhodovací Strom

```
Nová funkcionalita
├── Je to kritický byznys proces?
│   ├── ANO → DDD Událost
│   └── NE → Pokračuj
├── Potřebuje silnou typovou kontrolu?
│   ├── ANO → DDD Událost  
│   └── NE → Pokračuj
├── Bude se často měnit?
│   ├── ANO → Dynamická Událost
│   └── NE → Pokračuj
├── Má to konfigurovat uživatel?
│   ├── ANO → Dynamická Událost
│   └── NE → Pokračuj
└── Je to integrace/notifikace?
    ├── ANO → Dynamická Událost
    └── NE → Zvažte DDD Událost
```

## 🚀 Rychlý Start

### Pro Nový Kritický Proces:
1. Vytvořte DDD událost v `Abstractions/Events/Domain/`
2. Přidejte `AddDomainEvent()` do AggregateRoot metody
3. Vytvořte handler v `Application/Features/*/EventHandlers/`
4. Napište unit test

### Pro Novou Konfiguraci:
1. Přidejte `EventDefinition` do databáze
2. Vytvořte `Expression` pro podmínky (pokud potřeba)
3. Rozšiřte `DynamicEventHandler` o nový case
4. Otestujte v admin UI

---

**💡 Tip**: Když si nejste jisti, začněte s DDD událostí. Dynamickou můžete přidat později jako doplněk.
