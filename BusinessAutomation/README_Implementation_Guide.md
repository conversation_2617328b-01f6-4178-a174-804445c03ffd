# BusinessAutomation - Implementačn<PERSON>ůvodce

## Jak Implementovat DDD Doménové Události

### 1. Definice Události

```csharp
// BusinessAutomation.Core.Abstractions/Events/Domain/OrderPaidEvent.cs
using SharedKernel.Domain;

namespace BusinessAutomation.Core.Abstractions.Events.Domain
{
    /// <summary>
    /// Doménová událost vyvolaná při zaplacení objednávky.
    /// Kritický byznys proces - neměnný, silně typovaný.
    /// </summary>
    public class OrderPaidEvent : IDomainEvent
    {
        public Guid OrderId { get; }
        public decimal Amount { get; }
        public string PaymentMethod { get; }
        public DateTime PaidAt { get; }
        public string Currency { get; }

        public OrderPaidEvent(Guid orderId, decimal amount, string paymentMethod, 
                             DateTime paidAt, string currency = "CZK")
        {
            OrderId = orderId;
            Amount = amount;
            PaymentMethod = paymentMethod;
            PaidAt = paidAt;
            Currency = currency;
        }
    }
}
```

### 2. Vyvolání z AggregateRoot

```csharp
// Domain/Entities/Order.cs
public class Order : AggregateRoot
{
    public OrderStatus Status { get; private set; }
    public DateTime? PaidAt { get; private set; }
    public decimal TotalAmount { get; private set; }

    /// <summary>
    /// Označí objednávku jako zaplacenou.
    /// Klíčový byznys proces s doménovou událostí.
    /// </summary>
    public void MarkAsPaid(string paymentMethod, decimal paidAmount)
    {
        // Byznys validace
        if (Status == OrderStatus.Cancelled)
            throw new InvalidOperationException("Nelze zaplatit zrušenou objednávku");
            
        if (paidAmount != TotalAmount)
            throw new InvalidOperationException("Částka neodpovídá celkové sumě objednávky");

        // Změna stavu
        Status = OrderStatus.Paid;
        PaidAt = DateTime.UtcNow;

        // Vyvolání doménové události
        AddDomainEvent(new OrderPaidEvent(Id, paidAmount, paymentMethod, PaidAt.Value));
    }
}
```

### 3. Handler pro DDD Událost

```csharp
// Application/Features/Orders/EventHandlers/OrderPaidEventHandler.cs
using BusinessAutomation.Core.Abstractions.Events.Domain;
using MediatR;

namespace Application.Features.Orders.EventHandlers
{
    /// <summary>
    /// Handler pro doménovou událost zaplacení objednávky.
    /// Zpracovává kritické byznys procesy.
    /// </summary>
    public class OrderPaidEventHandler : INotificationHandler<OrderPaidEvent>
    {
        private readonly IEmailService _emailService;
        private readonly IInventoryService _inventoryService;
        private readonly ILogger<OrderPaidEventHandler> _logger;

        public OrderPaidEventHandler(
            IEmailService emailService,
            IInventoryService inventoryService,
            ILogger<OrderPaidEventHandler> logger)
        {
            _emailService = emailService;
            _inventoryService = inventoryService;
            _logger = logger;
        }

        public async Task Handle(OrderPaidEvent notification, CancellationToken cancellationToken)
        {
            _logger.LogInformation("Zpracování platby objednávky {OrderId}, částka {Amount} {Currency}",
                notification.OrderId, notification.Amount, notification.Currency);

            try
            {
                // Kritické byznys procesy
                await _inventoryService.ReserveItemsAsync(notification.OrderId, cancellationToken);
                await _emailService.SendPaymentConfirmationAsync(notification.OrderId, cancellationToken);
                
                _logger.LogInformation("Platba objednávky {OrderId} úspěšně zpracována", notification.OrderId);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Chyba při zpracování platby objednávky {OrderId}", notification.OrderId);
                throw; // Re-throw pro transaction rollback
            }
        }
    }
}
```

## Jak Konfigurovat Dynamické Datové Události

### 1. Definice v Databázi

```csharp
// Seed data nebo admin UI
var eventDefinitions = new[]
{
    new EventDefinition
    {
        EventName = "OrderStatusChanged",
        EntityType = typeof(Order),
        Operation = EventType.Update,
        TrackedProperties = new[] { "Status" },
        ConditionExpressionName = "OrderStatusNotification",
        Description = "Notifikace při změně stavu objednávky",
        IsActive = true
    },
    
    new EventDefinition
    {
        EventName = "LowStockAlert", 
        EntityType = typeof(Product),
        Operation = EventType.Update,
        TrackedProperties = new[] { "StockQuantity" },
        ConditionExpressionName = "LowStockCondition",
        Description = "Alert při nízkém stavu skladu",
        IsActive = true
    },
    
    new EventDefinition
    {
        EventName = "CustomerDataSync",
        EntityType = typeof(Customer),
        Operation = EventType.Update,
        TrackedProperties = new[] { "Email", "Phone", "Address" },
        Description = "Synchronizace zákaznických dat s externími systémy",
        IsActive = true
    }
};
```

### 2. Expression Engine Podmínky

```csharp
// Definice výrazů pro podmínky
var expressions = new[]
{
    new Expression
    {
        Name = "OrderStatusNotification",
        Description = "Podmínka pro notifikaci změny stavu objednávky",
        ExpressionTree = new OperationExpressionNode
        {
            Operator = "NotEqual",
            Operands = new[]
            {
                new SourceExpressionNode { SourcePath = "Status.OldValue" },
                new SourceExpressionNode { SourcePath = "Status.NewValue" }
            }
        }
    },
    
    new Expression
    {
        Name = "LowStockCondition", 
        Description = "Podmínka pro alert nízkého skladu",
        ExpressionTree = new OperationExpressionNode
        {
            Operator = "LessThan",
            Operands = new[]
            {
                new SourceExpressionNode { SourcePath = "StockQuantity" },
                new ConstantExpressionNode { Value = 10, DataType = "Integer" }
            }
        }
    }
};
```

### 3. Handler pro Dynamické Události

```csharp
// Infrastructure/Events/Handlers/DynamicEventHandler.cs
using BusinessAutomation.Core.Abstractions.Events.Models;

namespace Infrastructure.Events.Handlers
{
    /// <summary>
    /// Generický handler pro dynamické datové události.
    /// Zpracovává konfigurovatelné procesy.
    /// </summary>
    public class DynamicEventHandler : INotificationHandler<EntityChangedEvent>
    {
        private readonly IWebhookService _webhookService;
        private readonly INotificationService _notificationService;
        private readonly ILogger<DynamicEventHandler> _logger;

        public DynamicEventHandler(
            IWebhookService webhookService,
            INotificationService notificationService,
            ILogger<DynamicEventHandler> logger)
        {
            _webhookService = webhookService;
            _notificationService = notificationService;
            _logger = logger;
        }

        public async Task Handle(EntityChangedEvent notification, CancellationToken cancellationToken)
        {
            _logger.LogInformation("Zpracování dynamické události {EventName} pro entitu {EntityName}:{EntityId}",
                notification.EventName, notification.EntityName, notification.EntityId);

            try
            {
                // Routing na základě názvu události
                switch (notification.EventName)
                {
                    case "OrderStatusChanged":
                        await HandleOrderStatusChanged(notification, cancellationToken);
                        break;
                        
                    case "LowStockAlert":
                        await HandleLowStockAlert(notification, cancellationToken);
                        break;
                        
                    case "CustomerDataSync":
                        await HandleCustomerDataSync(notification, cancellationToken);
                        break;
                        
                    default:
                        await HandleGenericWebhook(notification, cancellationToken);
                        break;
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Chyba při zpracování dynamické události {EventName}", notification.EventName);
                // Neházeme výjimku - dynamické události nesmí rozbít hlavní proces
            }
        }

        private async Task HandleOrderStatusChanged(EntityChangedEvent evt, CancellationToken cancellationToken)
        {
            var statusChange = evt.ChangedProperties["Status"];
            await _notificationService.SendOrderStatusNotificationAsync(
                evt.EntityId, statusChange, cancellationToken);
        }

        private async Task HandleLowStockAlert(EntityChangedEvent evt, CancellationToken cancellationToken)
        {
            var stockQuantity = evt.ChangedProperties["StockQuantity"];
            await _notificationService.SendLowStockAlertAsync(
                evt.EntityId, stockQuantity, cancellationToken);
        }

        private async Task HandleCustomerDataSync(EntityChangedEvent evt, CancellationToken cancellationToken)
        {
            await _webhookService.SendCustomerDataSyncAsync(
                evt.EntityId, evt.ChangedProperties, cancellationToken);
        }

        private async Task HandleGenericWebhook(EntityChangedEvent evt, CancellationToken cancellationToken)
        {
            await _webhookService.SendGenericWebhookAsync(evt, cancellationToken);
        }
    }
}
```

## Rozhodovací Matice

### Kdy Použít DDD Události

| Kritérium | DDD Události |
|-----------|--------------|
| **Stabilita** | Neměnné byznys procesy |
| **Kritičnost** | Kritické pro fungování aplikace |
| **Typování** | Potřeba silné typové kontroly |
| **Vlastnictví** | Vývojářská odpovědnost |
| **Testování** | Unit testy, mock objekty |
| **Výkon** | Vysoký výkon, kompilované |

**Příklady**: Platby, zrušení objednávek, dokončení dodávek, registrace uživatelů

### Kdy Použít Dynamické Události

| Kritérium | Dynamické Události |
|-----------|-------------------|
| **Flexibilita** | Často se měnící požadavky |
| **Konfigurace** | Uživatelská konfigurace |
| **Integrace** | Externí systémy, webhooks |
| **Vlastnictví** | Administrátorská odpovědnost |
| **Testování** | Integration testy, end-to-end |
| **Škálovatelnost** | Velké množství různých událostí |

**Příklady**: Notifikace, alerting, synchronizace dat, webhooks, reportování

## Monitoring a Troubleshooting

### DDD Události - Debugging
```csharp
// Logování v DomainEventPublisher
_logger.LogDebug("Publikování doménové události {EventType} pro entitu {EntityId}", 
    domainEvent.GetType().Name, entityId);
```

### Dynamické Události - Debugging  
```csharp
// Logování v TrackableEntityInterceptor
_logger.LogDebug("Detekována změna entity {EntityType}:{EntityId}, vlastnosti: {Properties}",
    entityType, entityId, string.Join(", ", changedProperties));
```

### Metriky
- Počet vyvolaných událostí podle typu
- Doba zpracování handlerů
- Počet neúspěšných zpracování
- Webhook delivery rate

---

**Tip**: Začněte s DDD událostmi pro kritické procesy, dynamické události přidejte postupně podle potřeby konfigurace.
