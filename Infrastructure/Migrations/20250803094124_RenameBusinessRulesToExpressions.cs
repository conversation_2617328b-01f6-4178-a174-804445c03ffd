using System;
using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace Infrastructure.Migrations
{
    /// <inheritdoc />
    public partial class RenameBusinessRulesToExpressions : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            // Přejmenování tabulky BusinessRules na Expressions
            migrationBuilder.RenameTable(
                name: "BusinessRules",
                newName: "Expressions");

            // Přejmenování indexů
            migrationBuilder.RenameIndex(
                name: "IX_BusinessRules_EffectiveFrom",
                table: "Expressions",
                newName: "IX_Expressions_EffectiveFrom");

            migrationBuilder.RenameIndex(
                name: "IX_BusinessRules_EffectiveTo",
                table: "Expressions",
                newName: "IX_Expressions_EffectiveTo");

            migrationBuilder.RenameIndex(
                name: "IX_BusinessRules_IsActive",
                table: "Expressions",
                newName: "IX_Expressions_IsActive");

            migrationBuilder.RenameIndex(
                name: "IX_BusinessRules_Name",
                table: "Expressions",
                newName: "IX_Expressions_Name");

            migrationBuilder.RenameIndex(
                name: "IX_BusinessRules_Name_Version",
                table: "Expressions",
                newName: "IX_Expressions_Name_Version");

            migrationBuilder.RenameIndex(
                name: "IX_BusinessRules_TargetEntity_Active",
                table: "Expressions",
                newName: "IX_Expressions_TargetEntity_Active");

            migrationBuilder.RenameIndex(
                name: "IX_BusinessRules_TargetEntityName",
                table: "Expressions",
                newName: "IX_Expressions_TargetEntityName");

            migrationBuilder.RenameIndex(
                name: "IX_BusinessRules_Version",
                table: "Expressions",
                newName: "IX_Expressions_Version");

            // Aktualizace komentářů sloupců
            migrationBuilder.AlterColumn<string>(
                name: "Name",
                table: "Expressions",
                type: "TEXT",
                maxLength: 200,
                nullable: false,
                comment: "Název výrazu",
                oldClrType: typeof(string),
                oldType: "TEXT",
                oldMaxLength: 200,
                oldComment: "Název obchodního pravidla");

            migrationBuilder.AlterColumn<string>(
                name: "Description",
                table: "Expressions",
                type: "TEXT",
                maxLength: 1000,
                nullable: true,
                comment: "Popis účelu a použití výrazu",
                oldClrType: typeof(string),
                oldType: "TEXT",
                oldMaxLength: 1000,
                oldNullable: true,
                oldComment: "Popis účelu a použití pravidla");

            migrationBuilder.AlterColumn<string>(
                name: "TargetEntityName",
                table: "Expressions",
                type: "TEXT",
                maxLength: 100,
                nullable: false,
                comment: "Název cílové entity pro aplikaci výrazu",
                oldClrType: typeof(string),
                oldType: "TEXT",
                oldMaxLength: 100,
                oldComment: "Název cílové entity pro aplikaci pravidla");

            migrationBuilder.AlterColumn<string>(
                name: "TargetProperty",
                table: "Expressions",
                type: "TEXT",
                maxLength: 100,
                nullable: true,
                comment: "Název vlastnosti cílové entity pro výsledek výrazu",
                oldClrType: typeof(string),
                oldType: "TEXT",
                oldMaxLength: 100,
                oldNullable: true,
                oldComment: "Název vlastnosti cílové entity pro výsledek pravidla");

            migrationBuilder.AlterColumn<string>(
                name: "SchemaVersion",
                table: "Expressions",
                type: "TEXT",
                maxLength: 20,
                nullable: false,
                defaultValue: "1.0",
                comment: "Verze schématu výrazu pro kompatibilitu",
                oldClrType: typeof(string),
                oldType: "TEXT",
                oldMaxLength: 20,
                oldDefaultValue: "1.0",
                oldComment: "Verze schématu pravidla pro kompatibilitu");

            migrationBuilder.AlterColumn<string>(
                name: "RootNode",
                table: "Expressions",
                type: "TEXT",
                nullable: false,
                comment: "Kořenový uzel výrazu serializovaný jako JSON",
                oldClrType: typeof(string),
                oldType: "TEXT",
                oldComment: "Kořenový uzel pravidla serializovaný jako JSON");

            migrationBuilder.AlterColumn<bool>(
                name: "IsActive",
                table: "Expressions",
                type: "INTEGER",
                nullable: false,
                defaultValue: true,
                comment: "Určuje, zda je výraz aktivní",
                oldClrType: typeof(bool),
                oldType: "INTEGER",
                oldDefaultValue: true,
                oldComment: "Určuje, zda je pravidlo aktivní");

            migrationBuilder.AlterColumn<int>(
                name: "Version",
                table: "Expressions",
                type: "INTEGER",
                nullable: false,
                defaultValue: 1,
                comment: "Číslo verze výrazu pro podporu verzování",
                oldClrType: typeof(int),
                oldType: "INTEGER",
                oldDefaultValue: 1,
                oldComment: "Číslo verze pravidla pro podporu verzování");

            migrationBuilder.AlterColumn<DateTime>(
                name: "EffectiveFrom",
                table: "Expressions",
                type: "TEXT",
                nullable: false,
                comment: "Datum a čas, od kterého je tato verze výrazu platná",
                oldClrType: typeof(DateTime),
                oldType: "TEXT",
                oldComment: "Datum a čas, od kterého je tato verze pravidla platná");

            migrationBuilder.AlterColumn<DateTime>(
                name: "EffectiveTo",
                table: "Expressions",
                type: "TEXT",
                nullable: true,
                comment: "Datum a čas, do kterého je tato verze výrazu platná",
                oldClrType: typeof(DateTime),
                oldType: "TEXT",
                oldNullable: true,
                oldComment: "Datum a čas, do kterého je tato verze pravidla platná");

            // Poznámka: Tato migrace přejmenuje tabulku BusinessRules na Expressions
            // a zachová všechna existující data
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            // Vrácení komentářů sloupců zpět na původní hodnoty
            migrationBuilder.AlterColumn<string>(
                name: "Name",
                table: "Expressions",
                type: "TEXT",
                maxLength: 200,
                nullable: false,
                comment: "Název obchodního pravidla",
                oldClrType: typeof(string),
                oldType: "TEXT",
                oldMaxLength: 200,
                oldComment: "Název výrazu");

            migrationBuilder.AlterColumn<string>(
                name: "Description",
                table: "Expressions",
                type: "TEXT",
                maxLength: 1000,
                nullable: true,
                comment: "Popis účelu a použití pravidla",
                oldClrType: typeof(string),
                oldType: "TEXT",
                oldMaxLength: 1000,
                oldNullable: true,
                oldComment: "Popis účelu a použití výrazu");

            // Přejmenování indexů zpět
            migrationBuilder.RenameIndex(
                name: "IX_Expressions_EffectiveFrom",
                table: "Expressions",
                newName: "IX_BusinessRules_EffectiveFrom");

            migrationBuilder.RenameIndex(
                name: "IX_Expressions_EffectiveTo",
                table: "Expressions",
                newName: "IX_BusinessRules_EffectiveTo");

            migrationBuilder.RenameIndex(
                name: "IX_Expressions_IsActive",
                table: "Expressions",
                newName: "IX_BusinessRules_IsActive");

            migrationBuilder.RenameIndex(
                name: "IX_Expressions_Name",
                table: "Expressions",
                newName: "IX_BusinessRules_Name");

            migrationBuilder.RenameIndex(
                name: "IX_Expressions_Name_Version",
                table: "Expressions",
                newName: "IX_BusinessRules_Name_Version");

            migrationBuilder.RenameIndex(
                name: "IX_Expressions_TargetEntity_Active",
                table: "Expressions",
                newName: "IX_BusinessRules_TargetEntity_Active");

            migrationBuilder.RenameIndex(
                name: "IX_Expressions_TargetEntityName",
                table: "Expressions",
                newName: "IX_BusinessRules_TargetEntityName");

            migrationBuilder.RenameIndex(
                name: "IX_Expressions_Version",
                table: "Expressions",
                newName: "IX_BusinessRules_Version");

            // Přejmenování tabulky zpět na BusinessRules
            migrationBuilder.RenameTable(
                name: "Expressions",
                newName: "BusinessRules");
        }
    }
}
