using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace Infrastructure.Migrations
{
    /// <inheritdoc />
    public partial class SimplifyExpressionEntity : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            // Odstranění indexů závislých na TargetEntityName
            migrationBuilder.DropIndex(
                name: "IX_Expressions_TargetEntityName",
                table: "Expressions");

            migrationBuilder.DropIndex(
                name: "IX_Expressions_TargetEntity_Active",
                table: "Expressions");

            // Odstranění sloupců TargetEntityName a TargetProperty
            migrationBuilder.DropColumn(
                name: "TargetEntityName",
                table: "Expressions");

            migrationBuilder.DropColumn(
                name: "TargetProperty",
                table: "Expressions");

            // Přejmenování sloupce RootNode na ExpressionTree
            migrationBuilder.RenameColumn(
                name: "RootNode",
                table: "Expressions",
                newName: "ExpressionTree");

            // Aktualizace komentáře pro přejmenovaný sloupec
            migrationBuilder.AlterColumn<string>(
                name: "ExpressionTree",
                table: "Expressions",
                type: "TEXT",
                nullable: false,
                comment: "Strom výrazu serializovaný jako JSON",
                oldClrType: typeof(string),
                oldType: "TEXT",
                oldComment: "Kořenový uzel výrazu serializovaný jako JSON");
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            // Přejmenování sloupce zpět na RootNode
            migrationBuilder.RenameColumn(
                name: "ExpressionTree",
                table: "Expressions",
                newName: "RootNode");

            // Přidání sloupců TargetEntityName a TargetProperty zpět
            migrationBuilder.AddColumn<string>(
                name: "TargetEntityName",
                table: "Expressions",
                type: "TEXT",
                maxLength: 100,
                nullable: false,
                defaultValue: "",
                comment: "Název cílové entity pro aplikaci výrazu");

            migrationBuilder.AddColumn<string>(
                name: "TargetProperty",
                table: "Expressions",
                type: "TEXT",
                maxLength: 100,
                nullable: true,
                comment: "Název vlastnosti cílové entity pro výsledek výrazu");

            // Obnovení komentáře pro RootNode
            migrationBuilder.AlterColumn<string>(
                name: "RootNode",
                table: "Expressions",
                type: "TEXT",
                nullable: false,
                comment: "Kořenový uzel výrazu serializovaný jako JSON",
                oldClrType: typeof(string),
                oldType: "TEXT",
                oldComment: "Strom výrazu serializovaný jako JSON");

            // Obnovení indexů
            migrationBuilder.CreateIndex(
                name: "IX_Expressions_TargetEntityName",
                table: "Expressions",
                column: "TargetEntityName");

            migrationBuilder.CreateIndex(
                name: "IX_Expressions_TargetEntity_Active",
                table: "Expressions",
                columns: new[] { "TargetEntityName", "IsActive" });
        }
    }
}
