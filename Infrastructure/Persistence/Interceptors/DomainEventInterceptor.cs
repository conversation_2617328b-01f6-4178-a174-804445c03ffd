using Domain.Events;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.ChangeTracking;
using Microsoft.EntityFrameworkCore.Diagnostics;
using SharedKernel.Domain;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;

namespace Infrastructure.Persistence.Interceptors;

/// <summary>
/// Interceptor pro automatické generování doménových událostí.
/// Zjednodušená verze bez závislosti na BusinessAutomation Events systému.
/// </summary>
public class DomainEventInterceptor : SaveChangesInterceptor
{
    /// <summary>
    /// Inicializuje novou instanci DomainEventInterceptor.
    /// </summary>
    public DomainEventInterceptor()
    {
    }

    /// <summary>
    /// Asynchronní zpracování před uložením změn - generuje doménové události.
    /// </summary>
    public override async ValueTask<InterceptionResult<int>> SavingChangesAsync(
        DbContextEventData eventData,
        InterceptionResult<int> result,
        CancellationToken cancellationToken = default)
    {
        var context = eventData.Context;
        if (context == null)
            return result;

        await GenerateDomainEventsAsync(context);
        return result;
    }

    /// <summary>
    /// Synchronní zpracování před uložením změn - generuje doménové události.
    /// </summary>
    public override InterceptionResult<int> SavingChanges(
        DbContextEventData eventData,
        InterceptionResult<int> result)
    {
        var context = eventData.Context;
        if (context == null)
            return result;

        GenerateDomainEvents(context);
        return result;
    }

    /// <summary>
    /// Generuje doménové události pro změněné entity (asynchronní verze).
    /// Zjednodušená verze bez BusinessAutomation Events systému.
    /// </summary>
    /// <param name="context">Databázový kontext</param>
    /// <param name="cancellationToken">Token pro zrušení operace</param>
    private async Task GenerateDomainEventsAsync(DbContext context, CancellationToken cancellationToken = default)
    {
        // Zjednodušená implementace - pouze logování změn
        var entries = context.ChangeTracker.Entries()
            .Where(e => e.State == EntityState.Added ||
                       e.State == EntityState.Modified ||
                       e.State == EntityState.Deleted)
            .ToList();

        // Pro budoucí rozšíření - zde by se mohly generovat základní doménové události
        await Task.CompletedTask;
    }

    /// <summary>
    /// Generuje doménové události pro změněné entity (synchronní verze).
    /// </summary>
    /// <param name="context">Databázový kontext</param>
    private void GenerateDomainEvents(DbContext context)
    {
        // Pro synchronní verzi použijeme GetAwaiter().GetResult()
        GenerateDomainEventsAsync(context, CancellationToken.None).GetAwaiter().GetResult();
    }
}
